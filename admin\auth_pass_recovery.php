<?php
include('lib/db_connection.php');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title><?php echo SITE;?> - Login</title>
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico"/>
   <!-- BEGIN GLOBAL MANDATORY STYLES -->
   <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">

   <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />



    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />



    <link href="assets/css/authentication/form-2.css" rel="stylesheet" type="text/css" />



    <!-- END GLOBAL MANDATORY STYLES -->



    <link rel="stylesheet" type="text/css" href="assets/css/forms/theme-checkbox-radio.css">



    <link rel="stylesheet" type="text/css" href="assets/css/forms/switches.css">



</head>



<body class="form">



    







    <div class="form-container outer">



        <div class="form-form">



            <div class="form-form-wrap">



                <div class="form-container">
                    <div class="form-content">
                     <h1 class="">Password Recovery</h1>
                      <p class="">Enter your email and instructions will sent to you!</p>
                       <form class="text-left" id="loginform">
                            <div class="form">
                                <div id="username-field" class="field-wrapper input">
                                 <label for="username">Email</label>
                                   <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-at-sign"><circle cx="12" cy="12" r="4"></circle><path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-3.92 7.94"></path></svg>
                                    <input id="email" name="email" type="email" class="form-control" placeholder="Email" required>



                                </div>
                                <div class="d-sm-flex justify-content-between">
                                    <div class="field-wrapper">
                                        <button type="submit" class="btn btn-primary submit" value="">RESET</button>



                                    </div>



                                </div>



                            </div>



                        </form>



  <div class="loader_login"></div>



                    </div>                    



                </div>



            </div>



        </div>



    </div>







    



    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->



    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>



    <script src="bootstrap/js/popper.min.js"></script>



    <script src="bootstrap/js/bootstrap.min.js"></script>



    



    <!-- END GLOBAL MANDATORY SCRIPTS -->



    <script src="assets/js/authentication/form-2.js"></script>



<script>
 $("#loginform").submit(function(e) {
	$(".submit").attr('disabled',true);
	$(".loader_login").html('<i class="spinner-border text-primary  align-self-center"></i> please wait...');	
	 $.ajax({
	  url:'ajax/forgot_password.php',
	  type:'post',
	  data:$("#loginform").serialize(),
	  success:function(data){
	if(data==1)
		{  

		 $(".submit").attr('disabled',false)
		 $(".loader_login").html('<div class="alert alert-success" role="alert">Password sent to your email address!</div>');
setTimeout(function() {
      $(".loader_login").html('');	 
					}, 8000);
		}else{

	 $(".submit").attr('disabled',false);
	$(".loader_login").html('<div class="alert alert-danger" role="alert">E-mail does not exist!</div>');		
setTimeout(function() {
      $(".loader_login").html('');	 
					}, 8000);	
			}
		},
	  });
   e.preventDefault(); // avoid to execute the actual submit of the form.
});
</script>



</body>



</html>