<?php
session_start();
include('../../admin/lib/db_connection.php');
include('../../admin/lib/get_functions.php');
include('../../admin/inc/resize-class.php');

$page = 2;
$sub_page = 22;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

if (isset($_REQUEST['action']) && $_REQUEST['action'] == 'submit') {

    $otp            =   mysqli_real_escape_string($con, $_REQUEST['otp']);
    $cotp           =    $_SESSION["otp"];

    $fname          =   $_SESSION["fname"];
    $lname          =   $_SESSION["lname"];
    $email          =   $_SESSION["email"];
    $phone          =   $_SESSION["phone"];
    $sponsor_code   =   $_SESSION["ref_code"];
    $password       =   $_SESSION["password"];
    $status         =   1;
    $user_type      =   1;
    $own_code       =   getToken(10);


    if ($otp == $cotp) {

        $coins = 0;
        $amount = 0;

        $result = dbQuery("INSERT INTO tabl_user SET fname='" . $fname . "',lname='" . $lname . "',email='" . $email . "',phone='" . $phone . "',own_code='" . $own_code . "',sponsor_code='" . $sponsor_code . "',password='" . $password . "',status='" . $status . "', date='" . $date . "'");
        $user_id = dbInsertId();


        if ($result) {
            $result2 = dbQuery("INSERT INTO tabl_wallet SET user_id='" . $user_id . "',user_type='" . $user_type . "',amount='" . $amount . "',status='" . $status . "', date='" . $date . "'");

            // echo "INSERT INTO tabl_user SET fname='" . $fname . "',lname='" . $lname . "',email='" . $email . "',phone='" . $phone . "',ref_code='" . $ref_code . "',password='" . $password . "',status='" . $status . "', date='" . $date . "'";

            echo 1 . '~' . $user_id;
        } else {
            echo 2;
        }
    } else {
        echo 0;
    }

    //echo "INSERT INTO tabl_user SET name='" . $name . "',email='" . $email . "',phone='" . $phone . "',password='" . $password . "',status='" . $status . "', date='".$date."'";

}




// $otp = $_REQUEST['digit-1'] . '' . $_REQUEST['digit-2'] . '' . $_REQUEST['digit-3'] . '' . $_REQUEST['digit-4'];

// $sel_otp = dbQuery("SELECT * FROM tabl_login_otp WHERE email='" . $_REQUEST['email'] . "' AND otp='" . $otp . "'");
// $num_otp = dbNumRows($sel_otp);
// if ($num_otp == 0) {
//     echo 5;
//     die();
// } else {

//     $upliner = dbQuery("SELECT id as upliner_id FROM tabl_user WHERE sponsor_id='" . $_REQUEST['ref_code'] . "'");
//     $res_upliner = dbFetchAssoc($upliner);


//     dbQuery("INSERT INTO tabl_user SET name='" . mysqli_real_escape_string($con, $_REQUEST['username']) . "',sponsor_id='" . mysqli_real_escape_string($con, $_REQUEST['username']) . "',upliner_id='" . $res_upliner['upliner_id'] . "',phone='" . mysqli_real_escape_string($con, $_REQUEST['phone']) . "',email='" . mysqli_real_escape_string($con, $_REQUEST['email']) . "',pincode='" . mysqli_real_escape_string($con, $_REQUEST['pincode']) . "',password='" . md5($_REQUEST['password']) . "',user_image='default.png',status=1,date_added='" . $date . "'");

//     $to = $_REQUEST['email'];
    
//     // Subject
//     $subject = 'Welcome to BTC Trade ';

//     // Message
//     $message = '<html>
//     <body>
//     <h1>BTC Trade </h1> 
//     <div class="text" style="padding: 0 3em;">
//     <h2>Welcome to BTC Trade !!</h2>
//     <p>Hello ' . ucfirst($_REQUEST['username']) . ',<br/>
//     Welcome to GET TOUCH we are greeting to you join us and very thankful to you trust us.</p> 
//     <p>Regards<br/>
//     Team BTC Trade </p>
//     </div>
//     </html>';

//     // To send HTML mail, the Content-type header must be set
//     $headers[] = 'MIME-Version: 1.0';
//     $headers[] = 'Content-type: text/html; charset=iso-8859-1';

//     // Additional headers
//     $headers[] = 'From: ' . SITE . ' <' . EMAIL . '>';

//     // Mail it
//     mail($to, $subject, $message, implode("\r\n", $headers));
//     echo 1;
// }
