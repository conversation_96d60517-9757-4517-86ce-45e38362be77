<?php 
session_start();
include('../admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d H:i:s');

$sel=dbQuery("SELECT * FROM tabl_user WHERE email='".$_REQUEST['email']."'");
$num=dbNumRows($sel);
if($num>0){
 $res=dbFetchAssoc($sel); 
 
 $reset_link=uniqid('fg_password_');
 dbQuery("UPDATE tabl_user SET reset_link='".$reset_link."',reset_status='0' WHERE email='".$_REQUEST['email']."'");
 
$to = $res['email']; // note the comma
// Subject
$subject = 'Password Recovery';
// Message
$message ='<html>
<body>
<h1><img src="https://zorabit.com/assets/images/logo/logo2.png" alt="Zorabit" style="width:200px;height:64px;"></h1> 
<div class="text" style="padding: 0 3em;">
<h2>Password Recovery!!</h2>
<p>Hello '.$res['name'].',<br/>
Here are your Password Reset Link:</p>
<p><a href="https://zorabit.com/reset_password.php?reset_link='.$reset_link.'&email='.$res['email'].'" target="_blank">https://zorabit.com/reset_password.php?reset_link='.$reset_link.'&email='.$res['email'].'</a></p>
<p>Regards<br/>
Team Zorabit</div>
</html>';
// To send HTML mail, the Content-type header must be set
$headers[] = 'MIME-Version: 1.0';
$headers[] = 'Content-type: text/html; charset=iso-8859-1';
// Additional headers
$headers[] = 'From: '.SITE.' <'.EMAIL.'>';
// Mail it
mail($to, $subject, $message, implode("\r\n", $headers));	 
echo 1;	
}else{
echo 2;
}
?>