<?php 
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page=3;
$sub_page=31;
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d');

$user_d=dbQuery("SELECT * FROM tabl_user WHERE id='".$_REQUEST['user_id']."'");
$res_user_d=dbFetchAssoc($user_d);



?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
<title><?php echo SITE; ?>- Users</title>
<link rel="icon" type="image/x-icon" href="assets/img/favicon.ico"/>
<link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
<script src="assets/js/loader.js"></script>

<!-- BEGIN GLOBAL MANDATORY STYLES -->
<link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
<link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
<!-- END GLOBAL MANDATORY STYLES -->

<!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
<link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
<link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
<!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

<link rel="stylesheet" type="text/css" href="plugins/table/datatable/datatables.css">
<link rel="stylesheet" type="text/css" href="plugins/table/datatable/dt-global_style.css">
<link rel="stylesheet" href="plugins/font-icons/fontawesome/css/regular.css">
<link rel="stylesheet" href="plugins/font-icons/fontawesome/css/fontawesome.css">
</head>
<body class="alt-menu sidebar-noneoverflow">
<?php include('inc/__header.php');?>
<!--  BEGIN MAIN CONTAINER  -->
<div class="main-container" id="container">
  <div class="overlay"></div>
  <div class="search-overlay"></div>
  
  <!--  BEGIN TOPBAR  -->
  <?php include('inc/__menu.php');?>
  <!--  END TOPBAR  --> 
  
  <!--  BEGIN CONTENT PART  -->
  <div id="content" class="main-content">
    <div class="layout-px-spacing">
      <nav class="breadcrumb-one" aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="home.php">Home</a></li>
          <li class="breadcrumb-item"><a href="users.php">All Users</a></li>
          <li class="breadcrumb-item active"><a href="javascript:void(0);">Users</a></li>
        </ol>
      </nav>
      <div class="account-settings-container layout-top-spacing">
        <div class="account-content">
          <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
            <div class="row">
              <div class="col-xl-12 col-lg-12 col-sm-12  layout-spacing">
                <div class="widget-content widget-content-area br-6">
                  <h4>Downline</h4>
 <?php           
$memid1=$_REQUEST['user_id'];// your id
function getTotalLeg1($memid1,$leg1){ 
     $sql1=dbQuery("select id from tabl_user where upliner_id='".$memid1."' and position='".$leg1."'  AND status=1");
      global $total1;
	   $total1=$total1+dbNumRows($sql1);
	  while($row1=dbFetchAssoc($sql1)){     
         if($row1['id']!=''){
           getTotalLeg1($row1['id'],'left');
           getTotalLeg1($row1['id'],'right');
          } 
	  }
	   return @$total1;
    }      

     $total1=0; 
    $left1=getTotalLeg1($memid1,"left");    
    $total_left1=$total1."</br>"; 

    $total1=0;
    $right1=getTotalLeg1($memid1,"right");  
    $total_right1=$total1; 

$total_downline1=$total_left1+$total_right1?>  
       
         <!--<div class="row">
                      <div class="col-md-4" style="color: #900;font-size: 15px;font-weight: bold;">Total Downline: <span style="color:#093;"><?php echo $total_downline1;?></span></div>
                      <div class="col-md-4" style="color: #900;font-size: 15px;font-weight: bold;">Total Left: <span style="color:#093;"><?php echo $total_left1;?></span></div>
                     <div class="col-md-4" style="color: #900;font-size: 15px;font-weight: bold;">Total Right: <span style="color:#093;"><?php echo $total_right1;?></span></div>
                  </div>-->   
                  
                 <div class="row">
                      <div class="col-md-2" style="color: #900;font-size: 15px;font-weight: bold;">User: <span style="color:#093;"><?php echo $res_user_d['name'];?></span></div>
                       <div class="col-md-2" style="color: #900;font-size: 15px;font-weight: bold;">Sponsor#: <span style="color:#093;"><?php echo $res_user_d['sponsor_id'];?></span></div>
                 </div>
            
                  
                  <div class="row">
                    <!--<div class="col-md-10">
                      <form method="post">
                        <div class="form-row">                        
                          <div class="col-auto">
                            <label class="sr-only" for="inlineFormInput">Level</label>
                            <select name="level_id" class="form-control" required style="height: 38px;padding: 3px 13px;">
                              <option value="">SEARCH BY Level</option>
                              <?php 						    for($j=1;$j<=10;$j++){									if(@$_REQUEST['level_id']==$j){										$selected='selected';										}else{											$selected='';											}								echo '<option value="'.$j.'" '.$selected.'>'.$j.' (Star)</option>';									}?>
                            </select>
                          </div>
                          <div class="col-auto">
                            <button type="submit" name="search_by_level" class="btn btn-success submit">Search</button>
                          </div>
                          <div class="col-auto">
                            <div class="loader"></div>
                          </div>
                        </div>
                      </form>
                    </div>-->
                  </div>
                        
                  <div class="table-responsive mb-4 mt-4">
                    <table id="zero-config" class="table table-hover" style="width:100%">
                      <thead>
                        <tr>
                          <th>Date</th>
                          <th>Name</th>
                          <th>Sponsor_ID</th>
                          <th>Referral_ID</th>
                          <th>Levels</th>
                          <th>Forex</th>                           
                          <th>Position</th>
                          <th>Membership</th>
                          <th>Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        <?php
					if(isset($_REQUEST['search_by_level'])){	
				    $memid=$_REQUEST['user_id'];
                        echo getTotalLegbylevel($memid,"left",$_REQUEST['level_id']); 
                        echo getTotalLegbylevel($memid,"right",$_REQUEST['level_id']);
					}else{
						$memid=$_REQUEST['user_id'];
                        echo getTotalLeg($memid,"left"); 
                        echo getTotalLeg($memid,"right");
						}
						?>
						<?php 
					function getTotalLeg($memid,$leg){ 
												 
						 $sql=dbQuery("select * from tabl_user where upliner_id='".$memid."' and position='".$leg."'");
						  while($row=dbFetchAssoc($sql)){	
						 if($row['id']!=''){	
							
							 $up_sponsor=dbQuery("SELECT sponsor_id FROM tabl_user WHERE id='".$row['upliner_id']."'");
							   $res_up_sponsor=dbFetchAssoc($up_sponsor);
		
							 
							 if($row['status']==0){
													 $membership='<div class="badge badge-warning">Disabled</div>';
														 }else{
													$membership='<div class="badge badge-success">Enabled</div>';
													  }
							 							 
								 echo '<tr>
										<td>'.date('d-m-Y H:i:s',strtotime($row['date_added'])).'</td>
										<td>'.$row['name'].'</td>
										<td>'.$row['sponsor_id'].'</td>
										<td>'.$res_up_sponsor['sponsor_id'].'</td>
										<td>'.get_user_level($_REQUEST['user_id'],$row['id']).'</td>
										<td style="width: 150px;">';
									
										$forex=dbQuery("SELECT * FROM tabl_forex_trading WHERE user_id='".$row['id']."' and DATE(end_date)>='".date('Y-m-d')."'");
							      while($res_forex=dbFetchAssoc($forex)){
										if($res_forex['invest']==100){
							    echo '&nbsp;<span class="badge badge-primary" style="margin-top:4px;">$'.$res_forex['invest'].'</span>';
							   }elseif($res_forex['invest']==200){
								   echo '&nbsp;<span class="badge badge-warning" style="margin-top:4px;">$'.$res_forex['invest'].'</span>';
								   }elseif($res_forex['invest']==300){
								   echo '&nbsp;<span class="badge badge-dark" style="margin-top:4px;">$'.$res_forex['invest'].'</span>';
								   }elseif($res_forex['invest']==400){
								   echo '&nbsp;<span class="badge badge-danger" style="margin-top:4px;">$'.$res_forex['invest'].'</span>';
								   }elseif($res_forex['invest']==500){
								   echo '&nbsp;<span class="badge badge-success" style="margin-top:4px;">$'.$res_forex['invest'].'</span>';
								   }else{
								   echo '';
								     }
								   }
										echo '</td>';
								  
										echo '<td>'.$row['position'].'</td>
										<td><div class="badge badge-success">'.$row['membership_id'].' Star</div></td>
										<td>'.$membership.'</td>
								 </tr>';
								 
							   getTotalLeg ($row['id'],'left');
							   getTotalLeg ($row['id'],'right');
							    }
							  } 
							}?>
                        </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <?php include('inc/__footer.php');?>
    </div>
  </div>
  <!--  END CONTENT PART  --> 
</div>

<?php 
function get_user_level($parent,$user_id){
$sel=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$parent."'");
  while($res=dbFetchAssoc($sel)){
	    if($res['id']==$user_id){
		           return 1;
		      }else{
			$sel1=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res['id']."'");
                   while($res1=dbFetchAssoc($sel1)){	
				    if($res1['id']==$user_id){
						return 2;
		      }else{
				 $sel2=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res1['id']."'");
                   while($res2=dbFetchAssoc($sel2)){	
				    if($res2['id']==$user_id){
						return 3;
		               }else{
						   
				 		$sel3=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res2['id']."'");
                   while($res3=dbFetchAssoc($sel3)){	
				    if($res3['id']==$user_id){
						return 4;
		               }else{
			              $sel4=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res3['id']."'");
                   while($res4=dbFetchAssoc($sel4)){	
				    if($res4['id']==$user_id){
						return 5;
		               }else{
			              $sel5=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res4['id']."'");
                   while($res5=dbFetchAssoc($sel5)){	
				    if($res5['id']==$user_id){
						return 6;
		               }else{
                           $sel6=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res5['id']."'");
                   while($res6=dbFetchAssoc($sel6)){	
				    if($res6['id']==$user_id){
						return 7;
		               }else{
						   
                            $sel7=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res6['id']."'");
                   while($res7=dbFetchAssoc($sel7)){	
				    if($res7['id']==$user_id){
						return 8;
		               }else{
 
			                          }
				                   }  
								   
							$sel8=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res7['id']."'");
                   while($res8=dbFetchAssoc($sel8)){	
				    if($res8['id']==$user_id){
						return 9;
		               }else{
 
			                          }
				                   }  
								   
							$sel9=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res8['id']."'");
                   while($res9=dbFetchAssoc($sel9)){	
				    if($res9['id']==$user_id){
						return 10;
		               }else{
 
			                          }
				                   } 
								   
								   
						$sel10=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res9['id']."'");
                   while($res10=dbFetchAssoc($sel10)){	
				    if($res10['id']==$user_id){
						return 11;
		               }else{
 
			                          }
				                   }  		   
								   	 
						$sel11=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res10['id']."'");
                   while($res11=dbFetchAssoc($sel11)){	
				    if($res11['id']==$user_id){
						return 12;
		               }else{
 
			                          }
				                   }  		   
								   	 			 
						$sel12=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res11['id']."'");
                   while($res12=dbFetchAssoc($sel12)){	
				    if($res12['id']==$user_id){
						return 13;
		               }else{
 
			                          }
				                   }  		   
								   	 			 
						$sel13=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res12['id']."'");
                   while($res13=dbFetchAssoc($sel13)){	
				    if($res13['id']==$user_id){
						return 14;
		               }else{
 
			                          }
				                   }  			   	   
						
					$sel14=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res13['id']."'");
                   while($res14=dbFetchAssoc($sel14)){	
				    if($res14['id']==$user_id){
						return 15;
		               }else{
 
			                          }
				                   }
						
				$sel15=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res14['id']."'");
                   while($res15=dbFetchAssoc($sel15)){	
				    if($res15['id']==$user_id){
						return 16;
		               }else{
 
			                          }
				                   }
									   
						$sel16=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res15['id']."'");
                   while($res16=dbFetchAssoc($sel16)){	
				    if($res16['id']==$user_id){
						return 17;
		               }else{
 
			                          }
				                   }
								   
						$sel17=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res16['id']."'");
                   while($res17=dbFetchAssoc($sel17)){	
				    if($res17['id']==$user_id){
						return 18;
		               }else{
 
			                          }
				                   }		   
					$sel18=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res17['id']."'");
                   while($res18=dbFetchAssoc($sel18)){	
				    if($res18['id']==$user_id){
						return 19;
		               }else{
 
			                          }
				                   }			   
					$sel19=dbQuery("SELECT id FROM tabl_user WHERE upliner_id='".$res18['id']."'");
                   while($res19=dbFetchAssoc($sel19)){	
				    if($res19['id']==$user_id){
						return 20;
		               }else{
 
			                          }
				                   }			   
								   
								   
								   
								   		    
			                          }
				                   }  
			                          }
				                   }
						 
			                    }
				              }
					       }
				        }					
			         }
				 }
		    }
	    }				  
	 }
   }
}



?>

<!-- END MAIN CONTAINER --> 
<!-- BEGIN GLOBAL MANDATORY SCRIPTS --> 
<script src="assets/js/libs/jquery-3.1.1.min.js"></script> 
<script src="bootstrap/js/popper.min.js"></script> 
<script src="bootstrap/js/bootstrap.min.js"></script> 
<script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script> 
<script src="assets/js/app.js"></script> 
<script>
        $(document).ready(function() {
            App.init();
        });
    </script> 
<script src="assets/js/custom.js"></script> 
<!-- END GLOBAL MANDATORY SCRIPTS --> 

<!-- BEGIN PAGE LEVEL SCRIPTS --> 
<script src="plugins/table/datatable/datatables.js"></script> 
<script>
        $('#zero-config').DataTable({
            "oLanguage": {
                "oPaginate": { "sPrevious": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>', "sNext": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>' },
                "sInfo": "Showing page _PAGE_ of _PAGES_",
                "sSearch": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
                "sSearchPlaceholder": "Search...",
               "sLengthMenu": "Results :  _MENU_",
            },
            "stripeClasses": [],
            "lengthMenu": [8, 10, 20, 50],
            "pageLength": 10 
        });
    </script> 
<!-- END PAGE LEVEL SCRIPTS --> 
<script>
    function delete_student(id)
	{
	var retVal = confirm("Are you sure want to delete.");
	if( retVal == true ){
      $.ajax({
	  url:'ajax/delete_student.php',
	  type:'post',
	  data:{'id':id},
	  success:function(data){
		  //alert(data);
		 if(data==1){
			 location.reload();
		  }
   		 },
 	 }); 
   }else{
        return false;
   }
 	
	}
    </script> 
<script>
function change_status(tabl,val,row_id){
		var retVal = confirm("Are you sure want to change status.");
	if( retVal == true ){
      $.ajax({
	  url:'ajax/manage_activate.php',
	  type:'post',
	  data:{'tabl':tabl,'val':val,'row_id':row_id},
	  success:function(data){
		  //alert(data);
		 if(data==1){
			 location.reload();
		  }
   		 },
 	 }); 
   }else{
        return false;
   } 	
}
</script>
</body>
</html>


