<?php 
session_start();
include('../admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d H:i:s');

$otp=rand(1000,9999);

$sel=dbQuery("SELECT * FROM tabl_user WHERE email='".$_REQUEST['email']."'");
$num=dbNumRows($sel);

if($num>0){
$res=dbFetchAssoc($sel);

dbQuery("DELETE FROM tabl_login_otp WHERE email='".$_REQUEST['email']."'");
dbQuery("INSERT INTO tabl_login_otp SET otp='".$otp."',email='".$_REQUEST['email']."'");
 
$_SESSION['forgot_otp']=$otp;
$_SESSION['forgot_email']=$_REQUEST['email'];
$_SESSION['forgot_phone']=$res['phone'];
 
$to = $_REQUEST['email'];
// Subject
$subject = 'Forgot Password OTP | BTC Trade ';
// Message
$message ='<html>
<body>
<h1>BTC Trade </h1> 
<div class="text" style="padding: 0 3em;">
<p>Here is your otp for reset password:</p> 
<h2>'.$otp.'</h2>
<p>Regards<br/>
Team BTC Trade </p>
</div>
</html>';
// To send HTML mail, the Content-type header must be set
$headers[] = 'MIME-Version: 1.0';
$headers[] = 'Content-type: text/html; charset=iso-8859-1';
// Additional headers
$headers[] = 'From: '.SITE.' <'.EMAIL.'>';
// Mail it
mail($to, $subject, $message, implode("\r\n", $headers)); 
 echo 1;	
}else{
	echo 2;
	}
?>