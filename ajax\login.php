<?php
session_start();
include('../admin/lib/db_connection.php');
$user = $_REQUEST['username'];
$password = $_REQUEST['password'];

$query = dbQuery("SELECT * FROM  `tabl_user` WHERE  email =  '" . $user . "' || phone =  '" . $user . "' || sponsor_id =  '" . $user . "'");

$num_rows = dbNumRows($query);

//echo $num_rows;

if ($num_rows) {

	$row = dbFetchAssoc($query);
	$passcheck = password_verify($password, $row['password']);

	if ($passcheck == 1) {

		if ($row['status'] == '1') {
			$_SESSION["user"] = $row['email'];
			// $_SESSION["name"] = $row['fname'] . ' ' . $row['lname'];
			$_SESSION["name"] = $row['name'];
			$_SESSION["user_id"] = $row['id'];
			$_SESSION["user_type"] = $row['status'];

			if (isset($_REQUEST['product_id'])) {
				$_SESSION["product_id"] = $_REQUEST['product_id'];
			}

			$expiry = time() + (7 * 24 * 60 * 60); // Cookie expiry time (e.g., 7 days)
			setcookie('username', $user, $expiry, '/');
			setcookie('password', $password, $expiry, '/');

			// echo "<script>alert('" . $_COOKIE['username'] . "');</script>";



			echo '1';
		} else {
			echo '4';
		}
	} else {

		echo '2';
	}
} else {

	echo '0';
}
