<?php
session_start();
include('../admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

/*amount: 4
trc_address: dfsfsdfdf45654ytrytrytrytry
email: <EMAIL>*/

$wallet_balance = dbQuery("SELECT sum(receive_amount-paid_amount) as ttlamount FROM tabl_main_wallet WHERE user_id='" . $_SESSION['user_id'] . "'");
$res_wallet_amount = dbFetchAssoc($wallet_balance);
if ($res_wallet_amount['ttlamount'] != "") {
	$m_wallet = $res_wallet_amount['ttlamount'];
} else {
	$m_wallet = '0.00';
}


$up_bal = round($m_wallet, 2);
$deduct = $_REQUEST['amount'] * W_CHARGES / 100;
$deduct1 = $_REQUEST['amount'] * S_CHARGES / 100;
$total_deduct = (float)$deduct + (float)$deduct1;


$amount = round((float)$_REQUEST['amount'] - (float)$total_deduct, 2);



if ($m_wallet < $_REQUEST['amount']) {
	echo '<span style="color:red">Insufficient Wallet Balance!</span>';
	die();
} else {
	if ($_REQUEST['amount'] < M_WITHRAW) {
		echo '<span style="color:red">Minimum withdraw amount is $' . M_WITHRAW . '!</span>';
		die();
	} else {

		$otp = $_REQUEST['digit-2'] . '' . $_REQUEST['digit-3'] . '' . $_REQUEST['digit-4'] . '' . $_REQUEST['digit-5'];
		$sel_otp = dbQuery("SELECT * FROM tabl_withdraw_otp WHERE email='" . $_REQUEST['email'] . "' AND otp='" . $otp . "'");
		$num_otp = dbNumRows($sel_otp);
		if ($num_otp == 0) {
			echo '<span style="color:red">Invalid OTP!</span>';
			die();
		} else {
			dbQuery("INSERT INTO tabl_withdrawl SET user_id='" . $_SESSION['user_id'] . "',amount='" . $amount . "',trc_address='" . $_REQUEST['trc_address'] . "',requested_at='" . $date . "',status=0");
			$wid = dbInsertId();

			dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $_SESSION['user_id'] . "',paid_amount='" . $_REQUEST['amount'] . "',type=2,type_id=5,w_id='" . $wid . "',narration='Withdraw Request',date_added='" . $date . "'");

			echo 1;
		}
	}
}
