<?php
session_start();
error_reporting(0);
include('admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

// Recursive function to calculate total investment for a user and its downlines up to 10 levels.
function getInvestmentForUser($userId, $level = 1, $maxLevel = 10)
{
    if ($level > $maxLevel) {
        return 0;
    }

    // Get the user's own investment from tabl_plan_transaction.
    $sql = "SELECT SUM(price) as total FROM tabl_plan_transaction WHERE user_id = '$userId'";
    $result = dbQuery($sql);
    $row = dbFetchAssoc($result);
    $investment = isset($row['total']) ? $row['total'] : 0;

    // Get the direct downlines of the user from tabl_user.
    $sql2 = "SELECT id FROM tabl_user WHERE upliner_id = '$userId'";
    $result2 = dbQuery($sql2);
    while ($child = dbFetchAssoc($result2)) {
        // Recursively add investments from each child (and their subsequent downlines).
        $investment += getInvestmentForUser($child['id'], $level + 1, $maxLevel);
    }

    return $investment;
}

// Main function to calculate leadership income based on the two hands.
function calculateLeadershipInvestment($topUserId)
{
    // Retrieve direct downlines (hands) of the top user.
    $sql = "SELECT id FROM tabl_user WHERE upliner_id = '$topUserId'";
    $result = dbQuery($sql);

    $hands = array(); // Array to store investment of each direct downline branch.
    while ($direct = dbFetchAssoc($result)) {
        // For each direct downline, calculate total investment including its entire branch (up to 10 levels).
        $investment = getInvestmentForUser($direct['id'], 1, 10);
        $hands[] = $investment;
    }

    // If there are no direct downlines, return zeros.
    if (empty($hands)) {
        return array('first_hand' => 0, 'second_hand' => 0, 'grand_total' => 0);
    }

    // Identify the hand with the maximum investment.
    $maxInvestment = max($hands);
    // Calculate the total investment across all hands.
    $grandTotal = array_sum($hands);
    // The second hand is the total investment from all other downline branches.
    $secondHand = $grandTotal - $maxInvestment;

    // Return the results.
    return array(
        'first_hand'  => $maxInvestment,
        'second_hand' => $secondHand,
        'grand_total' => $grandTotal
    );
}

// Example usage:
$topUserId = 1; // Replace with the actual top user id
$result = calculateLeadershipInvestment($topUserId);
echo "First Hand Total: " . $result['first_hand'] . " USD<br>";
echo "Second Hand Total: " . $result['second_hand'] . " USD<br>";
echo "Grand Total: " . $result['grand_total'] . " USD<br>";
