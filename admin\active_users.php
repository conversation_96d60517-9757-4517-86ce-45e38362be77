<?php 
session_start();
error_reporting(0);
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page=3;
$sub_page=32;
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title><?php echo SITE; ?> - Active Users </title>
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico"/>
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    
     <link rel="stylesheet" type="text/css" href="plugins/table/datatable/datatables.css">
    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/dt-global_style.css">
    
     <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/regular.css">
    <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/fontawesome.css">
</head>
<body class="alt-menu sidebar-noneoverflow">

  <?php include('inc/__header.php');?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include('inc/__menu.php');?>
        <!--  END TOPBAR  -->
        
        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">                
<nav class="breadcrumb-one" aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
        <li class="breadcrumb-item active"><a href="javascript:void(0);">Active Users</a></li>

    </ol>
</nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-sm-12  layout-spacing">
                                
                        <div class="widget-content widget-content-area br-6">
                             <h4>Active Users</h4>                         
                            <div class="table-responsive mb-4 mt-4">
                                <table id="zero-config" class="table table-hover" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>ID</th>                                            
                                            <th>Date</th>
                                            <th>Name</th>
                                            <th>SponsorID</th>
                                            <th>REFER_BY</th>
                                            <th>Mobile</th>
                                            <th>Email</th>
                                            <th>Details</th>
                                            <th>Wallet</th>
                                            <th>Password</th>
                                            <th>Status</th>  
                                            <th>Account</th>                                       
                                         </tr>
                                    </thead>
                                    <tbody>
                   <?php 
					  $sel=dbQuery("SELECT * FROM tabl_user WHERE status=1 ORDER BY id ASC");
							$i=1;
								while($res=dbFetchAssoc($sel)){
									 $upline=dbQuery("SELECT sponsor_id FROM tabl_user WHERE id='".$res['upliner_id']."'");
										$res_upline=dbFetchAssoc($upline);
										$main_wallet=dbQuery("SELECT sum(receive_amount-paid_amount) as balance_amount FROM tabl_main_wallet WHERE user_id='".$res['id']."'");
									  $res_main_wallet=dbFetchAssoc($main_wallet);
									    if($res_main_wallet['balance_amount']!=""){
											 $m_wallet=$res_main_wallet['balance_amount'];
											}else{
												 $m_wallet='0.00';
												}
										
										?> 
                                        
                                        
                                        <tr>
                                            <td><?php echo $i;?></td>
                                            <td><?php echo date('d-m-Y H:i:s',strtotime($res['date_added']));?></td>
                                            <td><div class="d-flex">
                                           <p class="align-self-center mb-0 admin-name"> <?php echo $res['name'];?> </p>
                                                </div></td>
                                                <td><?php echo $res['sponsor_id'];?></td>    
                                                <td><?php echo $res_upline['sponsor_id'];?></td>    
                                                <td><?php echo $res['phone'];?></td> 
                                                <td><?php echo $res['email'];?></td> 
                                                <td><a href="user_details.php?id=<?php echo $res['id'] ?>"><div class="badge badge-primary">Details</div></a><br/>
                                            
                                                </td> 
                                               <td> <a href="wallet_details.php?id=<?php echo $res['id'] ?>"><span class="badge badge-warning"><span><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M20 9c1.1 0 2 .9 2 2v2c0 1.1-.9 2-2 2v3a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2zm-2 0V6H4v12h14v-3h-2c-1.1 0-2-1.1-2-2v-1.968C14 9.9 14.9 9 16 9zm-2 4h2v-2h-2z"/></svg></span>&nbsp;<span>$<?php echo $m_wallet;?></span></span></a></td>  
                                                
                                                 <td><a href="user_change_password.php?id=<?php echo $res['id'] ?>"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-unlock"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 9.9-1"></path></svg></a></td>  
                                               <td><?php if($res['status']==0){?>
												<a href="javascript:void(0)" onClick="change_status('tabl_user',1,<?php echo $res['id']?>)"><div class="badge badge-warning">Disable</div></a>
											<?php }else{?>
												<a href="javascript:void(0)" onClick="change_status('tabl_user',0,<?php echo $res['id']?>)"><div class="badge badge-success">Enable</div></a>
											<?php }
												 ?></td>
                                                 <td><?php  if($res['is_lock']==1){?>
													 <a href="javascript:void(0)" onClick="lock_account(<?php echo $res['id'] ?>,0)" style="color: #F00;"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-slash"><circle cx="12" cy="12" r="10"></circle><line x1="4.93" y1="4.93" x2="19.07" y2="19.07"></line></svg></a>
													  <?php }else{ ?>
                                                     <a href="javascript:void(0)" onClick="lock_account(<?php echo $res['id'] ?>,1)" style="color: #70d070;"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-pocket"><path d="M4 3h16a2 2 0 0 1 2 2v6a10 10 0 0 1-10 10A10 10 0 0 1 2 11V5a2 2 0 0 1 2-2z"></path><polyline points="8 10 12 14 16 10"></polyline></svg></a>
                                                      <?php }?></td>
                                        </tr>
                                        <?php $i++; } ?>
                                    </tbody>                                    
                                </table>
                            </div>
                        </div>
                    </div>
                           </div>
                        </div>
                    </div>
               </div>
 <?php include('inc/__footer.php');?>
          </div>
          
        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->
 <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>
   
    <script>
        $(document).ready(function() {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL SCRIPTS -->
    <script src="plugins/table/datatable/datatables.js"></script>
    <script>
        $('#zero-config').DataTable({
            "oLanguage": {
                "oPaginate": { "sPrevious": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>', "sNext": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>' },
                "sInfo": "Showing page _PAGE_ of _PAGES_",
                "sSearch": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
                "sSearchPlaceholder": "Search...",
               "sLengthMenu": "Results :  _MENU_",
            },
            "stripeClasses": [],
            "lengthMenu": [8, 10, 20, 50],
            "pageLength": 10 
        });
    </script>
    <!-- END PAGE LEVEL SCRIPTS -->
<script>
    function delete_student(id)
	{
	var retVal = confirm("Are you sure want to delete.");
	if( retVal == true ){
      $.ajax({
	  url:'ajax/delete_student.php',
	  type:'post',
	  data:{'id':id},
	  success:function(data){
		  //alert(data);
		 if(data==1){
			 location.reload();
		  }
   		 },
 	 }); 
   }else{
        return false;
   }
 	
	}
    </script>
<script>
function change_status(tabl,val,row_id){
		var retVal = confirm("Are you sure want to change status.");
	if( retVal == true ){
      $.ajax({
	  url:'ajax/manage_activate.php',
	  type:'post',
	  data:{'tabl':tabl,'val':val,'row_id':row_id},
	  success:function(data){
		  //alert(data);
		 if(data==1){
			 location.reload();
		  }
   		 },
 	 }); 
   }else{
        return false;
   } 	
}
</script>
	
</body>
</html>