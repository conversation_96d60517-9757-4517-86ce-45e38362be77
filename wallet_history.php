<?php
@session_start();
include('./lib/auth.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');


?>

<!doctype html>
<html lang="en">

<head>
    <meta cha$et="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title> Our Services</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <link rel="stylesheet" href="assets/nav.css">
    <style>
        .fullbody1 {
            background: #823fb50d;
            height: 122vh;
        }

        .today {
            text-align: center;
            margin: 20px;
        }

        .today select {
            width: 100%;
            height: 40px;
            border: none;
            text-align: center;
            font-size: 19px;
        }

        .today option {
            background: #e7d7f0;
        }

        .card1 {
            margin: 20px;

            justify-content: center;

            background-color: #fff;
            box-shadow: 3px 4px #00000012;

            justify-content: space-between;

            font-size: 13px;
            font-weight: 500;
            padding: 10px;
        }

        .card2 {
            margin: 20px;

            justify-content: center;

            background-color: #fff;
            /* box-shadow: 3px 4px #00000012; */

            justify-content: space-between;

            font-size: 13px;
            font-weight: 500;
            padding: 10px;
        }

        .col {
            line-height: 7px;
        }

        .card1 p {
            margin-bottom: 5px;
        }

        .card2 p {
            margin-bottom: 5px;
        }
    </style>
</head>

<body>
    <div class="fullbody1">
        <div class="header">
            <div class="header-items d-flex align-items-center justify-content-between m-2">
                <div class="web w-10" onclick="window.location.href = 'index.php'">


                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-arrow-left"
                        viewBox="0 0 16 16">
                        <path fill-rule="evenodd"
                            d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z" />
                    </svg>
                </div>

                <div class="home w-90 d-flex justify-content-center">
                    <h4>History</h4>
                </div>

                <div class="web w-10" onclick="window.location.href = 'index.php'">


                    <!-- <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-headset"
                        viewBox="0 0 16 16">
                        <path
                            d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5" />
                    </svg> -->
                </div>

            </div>
        </div>

        <div class="today">
            <p>Wallet History</p>
        </div>

        <!-- <div class="today">
            <select name="day" id="day">
                <option value="Today">Today</option>
                <option value="Yesterday">Yesterday</option>
                <option value="">Then After Yesterday</option>
            </select>
        </div> -->

        <?php $sel_history = dbQuery("SELECT * FROM tabl_main_wallet WHERE user_id='" . $user['id'] . "' ORDER BY date_added DESC");
        while ($res_history = dbFetchAssoc($sel_history)) {
            if ($res_history['type_id'] == 1 || $res_history['type_id'] == 2) { ?>
                <div class="logo2 d-flex align-items-center">
                    <div class="logo1"> <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#FF9800"
                            class="bi bi-gift" viewBox="0 0 16 16">
                            <path
                                d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A2.968 2.968 0 0 1 3 2.506V2.5zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43a.522.522 0 0 0 .023.07zM9 3h2.932a.56.56 0 0 0 .023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0V3zM1 4v2h6V4H1zm8 0v2h6V4H9zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5V7zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5H7z" />
                        </svg> </div>
                    <div class="client-name client-name1 d-flex justify-content-between w-100 flex-column">
                        <div class="name">
                            <p class="mb-0">
                                <?php
                                if ($res_history['type_id'] == 1) {
                                    echo 'Self Bonus';
                                } elseif ($res_history['type_id'] == 2) {
                                    echo 'Promotion Rewards';
                                } ?>
                                <br> <?= $currencySymbols; ?>
                                <?php echo $res_history['receive_amount']; ?>
                            </p>
                        </div>
                        <div class="refresh">
                            <p class="mb-0">
                                <?php echo date("F j, Y, g:i a", strtotime($res_history['date_added'])); ?>
                            </p>
                        </div>
                    </div> <a href="javascript:void(0)" class="btn btn1 rebtn-design  ">Success</a>
                </div>
            <?php
            } elseif ($res_history['type_id'] == 3) {
            ?>
                <div class="logo2 d-flex align-items-center">
                    <div class="logo1"> <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#FF9800"
                            class="bi bi-gift" viewBox="0 0 16 16">
                            <path
                                d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A2.968 2.968 0 0 1 3 2.506V2.5zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43a.522.522 0 0 0 .023.07zM9 3h2.932a.56.56 0 0 0 .023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0V3zM1 4v2h6V4H1zm8 0v2h6V4H9zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5V7zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5H7z" />
                        </svg> </div>
                    <div class="client-name client-name1 d-flex justify-content-between w-100 flex-column">
                        <div class="name">
                            <p class="mb-0">Today's Income<br> <?= $currencySymbols; ?>
                                <?php echo $res_history['receive_amount']; ?>
                            </p>
                        </div>
                        <div class="refresh">
                            <p class="mb-0">
                                <?php echo date("F j, Y, g:i a", strtotime($res_history['date_added'])); ?>
                            </p>
                        </div>
                    </div> <a href="javascript:void(0)" class="btn btn1 rebtn-design  ">Success</a>
                </div>
            <?php } elseif ($res_history['type_id'] == 4) { ?>
                <div class="logo2 d-flex align-items-center">
                    <div class="logo1"> <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#FF9800"
                            class="bi bi-gift" viewBox="0 0 16 16">
                            <path
                                d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A2.968 2.968 0 0 1 3 2.506V2.5zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43a.522.522 0 0 0 .023.07zM9 3h2.932a.56.56 0 0 0 .023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0V3zM1 4v2h6V4H1zm8 0v2h6V4H9zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5V7zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5H7z" />
                        </svg> </div>
                    <div class="client-name client-name1 d-flex justify-content-between w-100 flex-column">
                        <div class="name">
                            <p class="mb-0">Quantum Reward<br> <?= $currencySymbols; ?>
                                <?php echo $res_history['receive_amount']; ?>
                            </p>
                        </div>
                        <div class="refresh">
                            <p class="mb-0">
                                <?php echo date("F j, Y, g:i a", strtotime($res_history['date_added'])); ?>
                            </p>
                        </div>
                    </div> <a href="javascript:void(0)" class="btn btn1 rebtn-design  ">Success</a>
                </div>
            <?php } elseif ($res_history['type_id'] == 5) { ?>
                <div class="logo2 d-flex align-items-center">
                    <div class="logo1"> <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#009688"
                            class="bi bi-arrow-up-right" viewBox="0 0 16 16">
                            <path fill-rule="evenodd"
                                d="M14 2.5a.5.5 0 0 0-.5-.5h-6a.5.5 0 0 0 0 1h4.793L2.146 13.146a.5.5 0 0 0 .708.708L13 3.707V8.5a.5.5 0 0 0 1 0v-6z" />
                        </svg> </div>
                    <div class="client-name client-name1 d-flex justify-content-between w-100 flex-column">
                        <div class="name">
                            <p class="mb-0">Withdraw <br> <?= $currencySymbols; ?>
                                <?php echo $res_history['paid_amount']; ?>
                            </p>
                        </div>
                        <div class="refresh">
                            <p class="mb-0">
                                <?php echo date("F j, Y, g:i a", strtotime($res_history['date_added'])); ?>
                            </p>
                        </div>
                    </div>
                    <?php if ($res_history['w_status'] == 0) { ?> <a href="javascript:void(0)"
                            class="btn  ">Requested</a>
                    <?php } elseif ($res_history['w_status'] == 1) { ?> <a href="javascript:void(0)"
                            class="btn btn1 rebtn-design  ">Success</a>
                    <?php } ?>
                </div>
            <?php
            } elseif ($res_history['type_id'] == 6) {
            ?>
                <div class="logo2 d-flex align-items-center">
                    <div class="logo1"> <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#FF9800"
                            class="bi bi-gift" viewBox="0 0 16 16">
                            <path
                                d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A2.968 2.968 0 0 1 3 2.506V2.5zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43a.522.522 0 0 0 .023.07zM9 3h2.932a.56.56 0 0 0 .023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0V3zM1 4v2h6V4H1zm8 0v2h6V4H9zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5V7zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5H7z" />
                        </svg> </div>
                    <div class="client-name client-name1 d-flex justify-content-between w-100 flex-column">
                        <div class="name">
                            <p class="mb-0">Extra Reward<br> <?= $currencySymbols; ?>
                                <?php echo $res_history['receive_amount']; ?>
                            </p>
                        </div>
                        <div class="refresh">
                            <p class="mb-0">
                                <?php echo date("F j, Y, g:i a", strtotime($res_history['date_added'])); ?>
                            </p>
                        </div>
                    </div> <a href="javascript:void(0)" class="btn btn1 rebtn-design  ">Success</a>
                </div>
            <?php
            } elseif ($res_history['type_id'] == 7) {
            ?>
                <div class="logo2 d-flex align-items-center">
                    <div class="logo1"> <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#FF9800"
                            class="bi bi-gift" viewBox="0 0 16 16">
                            <path
                                d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A2.968 2.968 0 0 1 3 2.506V2.5zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43a.522.522 0 0 0 .023.07zM9 3h2.932a.56.56 0 0 0 .023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0V3zM1 4v2h6V4H1zm8 0v2h6V4H9zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5V7zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5H7z" />
                        </svg> </div>
                    <div class="client-name client-name1 d-flex justify-content-between w-100 flex-column">
                        <div class="name">
                            <p class="mb-0">Upgrade Plan<br> <?= $currencySymbols; ?>
                                <?php echo $res_history['paid_amount']; ?>
                            </p>
                        </div>
                        <div class="refresh">
                            <p class="mb-0">
                                <?php echo date("F j, Y, g:i a", strtotime($res_history['date_added'])); ?>
                            </p>
                        </div>
                    </div> <a href="javascript:void(0)" class="btn btn1 rebtn-design  ">Success</a>
                </div>
            <?php

            } elseif ($res_history['type_id'] == 8) { ?>
                <div class="logo2 d-flex align-items-center">
                    <div class="logo1"> <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#FF9800"
                            class="bi bi-gift" viewBox="0 0 16 16">
                            <path
                                d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A2.968 2.968 0 0 1 3 2.506V2.5zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43a.522.522 0 0 0 .023.07zM9 3h2.932a.56.56 0 0 0 .023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0V3zM1 4v2h6V4H1zm8 0v2h6V4H9zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5V7zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5H7z" />
                        </svg> </div>
                    <div class="client-name client-name1 d-flex justify-content-between w-100 flex-column">
                        <div class="name">
                            <p class="mb-0">Level Income<br> <?= $currencySymbols; ?>
                                <?php echo $res_history['receive_amount']; ?>
                            </p>
                        </div>
                        <div class="refresh">
                            <p class="mb-0">
                                <?php echo date("F j, Y, g:i a", strtotime($res_history['date_added'])); ?>
                            </p>
                        </div>
                    </div> <a href="javascript:void(0)" class="btn btn1 rebtn-design  ">Success</a>
                </div>
            <?php
            } elseif ($res_history['type_id'] == 9) { ?>
                <div class="logo2 d-flex align-items-center">
                    <div class="logo1"> <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#FF9800"
                            class="bi bi-gift" viewBox="0 0 16 16">
                            <path
                                d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A2.968 2.968 0 0 1 3 2.506V2.5zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43a.522.522 0 0 0 .023.07zM9 3h2.932a.56.56 0 0 0 .023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0V3zM1 4v2h6V4H1zm8 0v2h6V4H9zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5V7zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5H7z" />
                        </svg> </div>
                    <div class="client-name client-name1 d-flex justify-content-between w-100 flex-column">
                        <div class="name">
                            <p class="mb-0">Leadership Income<br> <?= $currencySymbols; ?>
                                <?php echo $res_history['receive_amount']; ?>
                            </p>
                        </div>
                        <div class="refresh">
                            <p class="mb-0">
                                <?php echo date("F j, Y, g:i a", strtotime($res_history['date_added'])); ?>
                            </p>
                        </div>
                    </div> <a href="javascript:void(0)" class="btn btn1 rebtn-design  ">Success</a>
                </div>
            <?php
            } elseif ($res_history['type_id'] == 10) { ?>
                <div class="logo2 d-flex align-items-center">
                    <div class="logo1"> <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#FF9800"
                            class="bi bi-gift" viewBox="0 0 16 16">
                            <path
                                d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A2.968 2.968 0 0 1 3 2.506V2.5zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43a.522.522 0 0 0 .023.07zM9 3h2.932a.56.56 0 0 0 .023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0V3zM1 4v2h6V4H1zm8 0v2h6V4H9zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5V7zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5H7z" />
                        </svg> </div>
                    <div class="client-name client-name1 d-flex justify-content-between w-100 flex-column">
                        <div class="name">
                            <p class="mb-0"> Direct Joining Income<br> <?= $currencySymbols; ?>
                                <?php echo $res_history['receive_amount']; ?>
                            </p>
                        </div>
                        <div class="refresh">
                            <p class="mb-0">
                                <?php echo date("F j, Y, g:i a", strtotime($res_history['date_added'])); ?>
                            </p>
                        </div>
                    </div> <a href="javascript:void(0)" class="btn btn1 rebtn-design  ">Success</a>
                </div>
        <?php
            }
        }
        ?>

        <!-- <div class="card1 d-flex">
            <div class="lef">
                <p>Member Withdrawal</p>
                <p>2024-11-09 16:14:17</p>
            </div>
            <div class="right">
                <p style="color: red;">-100</p>
                <p>Balance: $335.34</p>
            </div>
        </div> -->



        <!-- <div class="card1 d-flex">
            <div class="lef">
                <p>Commission fee</p>
                <p>2024-11-09 16:14:17</p>
            </div>
            <div class="right">
                <p>+300</p>
                <p>Balance: $335.34</p>
            </div>

        </div>
        <div class="card1 d-flex">
            <div class="lef">
                <p>Order refund</p>
                <p>2024-11-09 16:14:17</p>
            </div>
            <div class="right">
                <p>+383</p>
                <p>Balance: $335.34</p>
            </div>
        </div>
        <div class="card1 d-flex">
            <div class="lef">
                <p>Submit the order</p>
                <p>2024-11-09 16:14:17</p>
            </div>
            <div class="right">
                <p style="color: red;">-383</p>
                <p>Balance: $335.34</p>
            </div>
        </div>
        <div class="card1 d-flex">
            <div class="lef">
                <p>Value-added refund</p>
                <p>2024-11-09 16:14:17</p>
            </div>
            <div class="right">
                <p>+300</p>
                <p>Balance: $335.34</p>
            </div>
        </div>
        <br>
        <p class="text-center">Loaded</p>
        <br>
        <div class="card2 d-flex">
            <div class="lef">
                <p>Total Recharge Amount</p>
                <p>Total Withdrawal Amount</p>
            </div>
            <div class="right">
                <p>0.00</p>
                <p>0.00</p>
            </div>
        </div> -->


    </div>




    <script src="assets/nav.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
        crossorigin="anonymous"></script>
</body>

</html>