<?php
include('admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

// createing a function to give bonus amount to user 
function addBonusToUserAccount ($userId,$amount,$toId)
{
    $date = date('Y-m-d H:i:s');
    return dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $userId . "',receive_amount='" . $amount . "',type=1,type_id=6,to_id='" . $toId . "',narration='Weekly Bonus',date_added='" . $date . "'"); //type_id=6 for weelky bonus
}


// getting all users details
$userSql = dbQuery("SELECT * FROM `tabl_user` WHERE status=1");
if(dbNumRows($userSql)>0){
    while($cUserData = dbFetchAssoc($userSql))
    {
        $userId = $cUserData['id'];  // user id
        $membershipId = $cUserData['membership_id']; // level id 
        $uplinerId = $cUserData['upliner_id']; // getting upliner id  of the user
         // setting level according 
         if($membershipId == "13") 
         {
            // giving bonus of 250 usdt 
            addBonusToUserAccount($uplinerId,250,$userId);
         }else if($membershipId<=12  && $membershipId>=10)
         {
            // giving bonus of 150 usdt 
            addBonusToUserAccount($uplinerId,150,$userId);
         }
         else if($membershipId<=9  && $membershipId>=7 )
         {
            // giving bonus of 50 usdt 
            addBonusToUserAccount($uplinerId,50,$userId);
         }
         else if($membershipId<=6  && $membershipId>=4 )
         {
            // giving bonus of 20 usdt 
            addBonusToUserAccount($uplinerId,20,$userId);
         }
         else if($membershipId<=3  && $membershipId>=1 )
         {
            // giving bonus of 10 usdt 
            addBonusToUserAccount($uplinerId,10,$userId);
         }
    }
}


// Task 1. join plan 10 time level 1 to 3 
// Weekly bonus 10 USDT
// Task 2.Join plan 8 time  level 4 to 6 
// Weekly bonus 20 usdt 
// Task 3. Join plan 5 time  level 7 to 9 
// Weekly bonus 50 usdt 
// Task 4 . Join plan 3 time  level 10 to 12 
// Weekly bonus 150 usdt 
// Task 5. Join plan level 13 one time 
// Weekly bonus 250 usdt membership_id
?>