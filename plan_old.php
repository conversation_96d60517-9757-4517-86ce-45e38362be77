<?php
@session_start();
include ('./lib/auth.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');


?>

<!doctype html>
<html lang="en">

<head>
  <meta cha$et="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Our Plans</title>
  <link rel="stylesheet" href="assets/style.css">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
  <link rel="stylesheet" href="assets/nav.css">
</head>
<style>
  .w-30 {
    width: 30%;
  }

  .w-70 {
    width: 70%;
  }

  .content-banner {
    margin: 20px;
    text-align: center;
    justify-content: center;
    align-items: center;
  }

  .content-banner .b-head {
    color: orange;
    margin-bottom: 2px;
    font-size: 21px;
  }

  .content-banner h1 {
    color: #823fb5;
    font-weight: 600;
    margin-bottom: 22px;
  }

  .content-banner p {
    color: #73717e;
    font-weight: 500;
  }

  .plan-card {
    margin: 20px;
    background-color: #fff;
    text-align: center;
    justify-content: center;
    align-items: center;
    padding: auto;
    border-radius: 22px;
    box-shadow: 0 0 10px rgb(17 55 209 / 10%);
  }

  .plan-card h1 {
    color: #823fb5;
    font-size: 17px;
    font-weight: 700;
    margin-top: 9px;
  }

  .plan-card h6 {
    color: rgb(68 18 149);
    font-size: 12px;
    font-weight: 700;
    line-height: 12px;
  }

  .plan-card p {
    color: #3e3854;
    margin-top: 4px;
    font-weight: 600;
    line-height: 7px;
  }

  .plan-card p span {
    background-color: green;
    width: 20%;
  }

  .level {

    background-color: #ff6500;
    border: none;
    color: #fff;
    width: 31%;
    height: 35px;
    font-size: 17px;
    font-weight: 500;
    margin-top: -24px;
    text-align: center;
    justify-content: center;
    align-items: center;
    border-radius: 0px 20px 30px 30px;
  }

  .level:hover {
    background-color: #823fb5;

  }

  .invest {
    background-color: #ff6500;
    border: none;
    color: #fff;
    width: 38%;
    height: 39px;
    font-size: 15px;
    font-weight: 500;
    /* margin-top: -24px; */
    text-align: center;
    justify-content: center;
    align-items: center;
    border-radius: 34px 0px 30px 30px;
    margin-bottom: -15px;

  }

  .invest:hover {
    background-color: #823fb5;

  }

  .no {
    display: flex;
    text-align: center;
    justify-content: center;
  }

  .life {
    margin-top: 20px;
  }

  .no button {
    width: 33px;
    height: 22px;
    border: none;
    font-size: 11px;
    background: #118549;
    color: white;
    border-radius: 6px;
    margin: 7px;
    margin-top: -3px;
  }

  .plan-card .disabled {
    background-color: #c19374;
    -webkit-user-select: none;
  }
</style>

<body>
  <div class="fullbody">
    <div class="header">
      <div class="header-items d-flex align-items-center justify-content-between m-2">
        <a href="index.php">
          <div class="web w-10">
            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-arrow-left"
              viewBox="0 0 16 16">
              <path fill-rule="evenodd"
                d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z" />
            </svg>
          </div>
        </a>

        <div class="home w-90 d-flex justify-content-center">
          <h4>Plans</h4>
        </div>

      </div>
    </div>
    <div class="content-banner">
      <p class="b-head">Shared Prosperity</p>
      <h1>CHOOSE PLAN </h1>
      <p>Choose wisely. ensuring transparency and sustainability for long-term financial growth</p>
    </div>
    <br>


    <?php


    $sel = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
    if ($res = dbFetchAssoc($sel)) {
      $exist_plan_id = $res['membership_id'];
    }

    $r_req_plan = 0;
    $requested_plan = dbQuery("SELECT * FROM tabl_plan_transaction WHERE user_id='" . $_SESSION['user_id'] . "' AND (status='1' OR status='0')");
    if (dbNumRows($requested_plan)) {
      $r_req_plan = 1;
    }


    $sel_plan = dbQuery("SELECT * FROM tabl_plans ORDER BY id ASC");
    $i = 1;
    while ($res_plan = dbFetchAssoc($sel_plan)) {

      if ($r_req_plan > 0) {

        $my_plan = dbQuery("SELECT * FROM tabl_plan_transaction WHERE user_id='" . $_SESSION['user_id'] . "' AND plan_id='" . $res_plan['id'] . "'");
        if ($r_res_plan = dbFetchAssoc($my_plan)) {

          if ($r_res_plan['status']) {
            if ($exist_plan_id == $res_plan['id']) {
              $text = 'Current Plan';
              $disabled = 'disabled';
            } else {
              $text = 'Previous Plan';
              $disabled = 'disabled';
            }
          } else {
            $text = 'Plan Requested';
            $disabled = 'disabled';
          }

        } else {
          $text = 'Invest Now';
          $disabled = 'disabled';
        }

        // $url = '<a href="javascript:void(0)" class="btn btn4 btn-primary ' . $disabled . '">' . $text . '</a>';
        $url = '';
        $state = "disabled";
      } else {

        // $url = '<a href="transaction.php?plan_id=' . $res_plan['id'] . '" class="btn btn4 btn-primary">' . $text . '</a>';
        $url = 'transaction.php?plan_id=' . $res_plan['id'];
        $state = "";
        $disabled = '';
        $text = 'Invest Now';
      }
      ?>
      <div class="plan-card  align-items-center">
        <button class="level">Level
          <?php echo $i; ?>
        </button>
        <h1>
          <?php echo $res_plan['price']; ?>$
        </h1>
        <h6>
          <?php echo $res_plan['daily_income']; ?> $ Daily Income
        </h6>
        <h6 class="mb-4">
          <?php echo $res_plan['daily_income'] * 30; ?> $ Monthly Income
        </h6>
        <div class="no">
          <p>Capital will back:</p>
          <button>No</button>
        </div>

        <p class="life">lifetime Earning</p>

        <button class="invest <?php echo $state . ' ' . $disabled; ?>"
          onclick="window.location.href = '<?php echo $url; ?>'" <?php echo $state; ?>>
          <?php echo $text; ?>
        </button>
        <!-- <button class="invest" onclick="window.location.href = 'deposit.php?plan_id=<?php echo $res_plan['id'] ?>'">Invest Now</button> -->

      </div>
      <br />
      <?php
      $i++;
    }
    ?>




  </div>




  <script src="assets/nav.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
    crossorigin="anonymous"></script>
</body>

</html>