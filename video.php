

<!doctype html>
<html lang="en">

<head>
    <meta cha$et="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title> Our Services</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <link rel="stylesheet" href="assets/nav.css">
       <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
      <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.0.943/pdf.min.js" integrity="sha512-0DCQV6q4G4VIvwQQOTUbfuROoPElJcmTWUt3MjMzdyREzcOVREUYr2bFuzYC7tDh+tigVrm+X1wnu8SRTexJuw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <style>
        .fullbody1 {
            background: #823fb50d;
            height: 122vh;
        }

        .today {
            text-align: center;
            margin: 20px;
        }

        .today select {
            width: 100%;
            height: 40px;
            border: none;
            text-align: center;
            font-size: 19px;
        }

        .today option {
            background: #e7d7f0;
        }

        .card1 {
            margin: 20px;

            justify-content: center;

            background-color: #fff;
            box-shadow: 3px 4px #00000012;

            justify-content: space-between;

            font-size: 13px;
            font-weight: 500;
            padding: 10px;
        }

        .card2 {
            margin: 20px;

            justify-content: center;

            background-color: #fff;
            /* box-shadow: 3px 4px #00000012; */

            justify-content: space-between;

            font-size: 13px;
            font-weight: 500;
            padding: 10px;
        }

        .col {
            line-height: 7px;
        }

        .card1 p {
            margin-bottom: 5px;
        }

        .card2 p {
            margin-bottom: 5px;
        }
    </style>
</head>

<body>
    <div class="fullbody1">
        <div class="header">
            <div class="header-items d-flex align-items-center justify-content-between m-2">
                <div class="web w-10" onclick="window.location.href = 'index.php'">


                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-arrow-left" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z" />
                    </svg>
                </div>

                <div class="home w-90 d-flex justify-content-center">
            
                    <h4>Tutorial Video</h4>
                </div>

                <div class="web w-10" onclick="window.location.href = 'index.php'">


                    <!-- <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-headset"
                        viewBox="0 0 16 16">
                        <path
                            d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5" />
                    </svg> -->
                </div>

            </div>
        </div>

       <div class="today d-flex justify-content-center align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-camera-video" viewBox="0 0 16 16">
  <path fill-rule="evenodd" d="M0 5a2 2 0 0 1 2-2h7.5a2 2 0 0 1 1.983 1.738l3.11-1.382A1 1 0 0 1 16 4.269v7.462a1 1 0 0 1-1.406.913l-3.111-1.382A2 2 0 0 1 9.5 13H2a2 2 0 0 1-2-2zm11.5 5.175 3.5 1.556V4.269l-3.5 1.556zM2 4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h7.5a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1z"/>
</svg>
            <p style="margin-left: 10px;">Tutorial Presentation #1</p>
        </div>

<div class="logo2 align-items-center">
<!--                    <div class="">-->
                  
<!--<embed src="presentation1.pdf" width="100%" height="100%" -->
<!-- type="application/pdf">-->


<!--                </div>-->
    <section class="tf-section table-product mb-pd-0">
                <div class="container">
                    <div class="row d-flex justify-content-center">
                        <div class="col-xl-12 col-md-12">
                            <div class="offer">

                                <div class="offer-img">
                             
                                    <div class="d-flex justify-content-between">

                                        <div id="my_pdf_viewer">
                                            <div id="navigation-controls">
                                                <span>Page:</span>
                                                <button id="go_pre"><i class="fa fa-chevron-left"></i></button>
                                                <input type="number" id="current_page" value="1">
                                                <button id="go_next"><i class="fa fa-chevron-right"></i></button>


                                                <span>Zoom:</span>
                                                <button id="zoom_out"><i class="fa-solid fa-minus"></i></button>
                                                <button id="zoom_in"><i class="fa-solid fa-plus"></i></button>
                                            </div>
                                            <div id="canvas_container">
                                                <canvas id="pdf_renderer" class="w-100">

                                                </canvas>
                                            </div>

                                        </div>


                                        <!-- <div id="pdf_viewer"></div> -->
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </section>
                </div>

        <div class="today d-flex justify-content-center align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-camera-video" viewBox="0 0 16 16">
  <path fill-rule="evenodd" d="M0 5a2 2 0 0 1 2-2h7.5a2 2 0 0 1 1.983 1.738l3.11-1.382A1 1 0 0 1 16 4.269v7.462a1 1 0 0 1-1.406.913l-3.111-1.382A2 2 0 0 1 9.5 13H2a2 2 0 0 1-2-2zm11.5 5.175 3.5 1.556V4.269l-3.5 1.556zM2 4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h7.5a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1z"/>
</svg>
            <p style="margin-left: 10px;">Tutorial Video #1</p>
        </div>

        <!-- <div class="today">
            <select name="day" id="day">
                <option value="Today">Today</option>
                <option value="Yesterday">Yesterday</option>
                <option value="">Then After Yesterday</option>
            </select>
        </div> -->

   

      

<div class="logo2 d-flex align-items-center">
                    <div class="">
                           <video width="100%" height="100%" controls>
<source src="asset/mixkit-a-woman-shows-how-to-use-a-moisturizing-skincare-product-50417-medium.mp4" type="video/mp4">


</video>
                </div>
                </div>
                       <div class="today d-flex justify-content-center align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-camera-video" viewBox="0 0 16 16">
  <path fill-rule="evenodd" d="M0 5a2 2 0 0 1 2-2h7.5a2 2 0 0 1 1.983 1.738l3.11-1.382A1 1 0 0 1 16 4.269v7.462a1 1 0 0 1-1.406.913l-3.111-1.382A2 2 0 0 1 9.5 13H2a2 2 0 0 1-2-2zm11.5 5.175 3.5 1.556V4.269l-3.5 1.556zM2 4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h7.5a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1z"/>
</svg>
            <p style="margin-left: 10px;">Tutorial Video #2</p>
        </div>
       
<div class="logo2 d-flex align-items-center">
                    <div class="">
                           <video width="100%" height="100%" controls>
<source src="asset/mixkit-a-woman-shows-how-to-use-a-moisturizing-skincare-product-50417-medium.mp4" type="video/mp4">


</video>
                </div>
     


    </div>



    <script>
        var myState = {
            pdf: null,
            currentPage: 1,
            zoom: 0.5
        }
        var pdfPath = "presentation1.pdf";

        pdfjsLib.getDocument(pdfPath).then(pdf => {
            myState.pdf = pdf;
            render()

        });

        function render() {
            myState.pdf.getPage(myState.currentPage).then(page => {
                var canvas = document.getElementById('pdf_renderer');
                var cts = canvas.getContext('2d');
                var viewport = page.getViewport(myState.zoom);

                canvas.width = viewport.width;
                canvas.height = viewport.height;

                page.render({
                    canvasContext: cts,
                    viewport: viewport

                })
            })
        }

        document.getElementById('go_pre').addEventListener('click', (e) => {
            if (myState.pdf == null || myState.currentPage == 1) return;

            myState.currentPage = myState.currentPage - 1;
            document.getElementById('current_page').value = myState.currentPage;

            render();
        });

        document.getElementById('go_next').addEventListener('click', (e) => {
            if (myState.pdf == null || myState.currentPage >= myState.pdf._pdfInfo.numPages) return;

            myState.currentPage = myState.currentPage + 1;
            document.getElementById('current_page').value = myState.currentPage;

            render();
        });

        document.getElementById('current_page').addEventListener('keypress', (e) => {
            // if (myState.pdf == null) return;

            var code = (e.keyCode ? e.keyCode : e.which)

            if (code == 13) {
                var desiredPage = parseInt(document.getElementById('current_page').value);

                if (desiredPage >= 1 && desiredPage <= myState.pdf._pdfInfo.numPages) {

                    myState.currentPage = desiredPage;
                    document.getElementById('current_page').value = desiredPage;

                    // alert(desiredPage);
                    // alert(myState.pdf._pdfInfo.numPages);
                    render();
                }
                // alert(desiredPage);
            }

            myState.currentPage = myState.currentPage + 1;
            // document.getElementById('current_page').value = desiredPage;

        });

        document.getElementById('zoom_in').addEventListener('click', (e) => {
            if (myState.pdf == null) return;

            if (myState.zoom < 2.5) {
                myState.zoom = myState.zoom + 0.1;
                render();
            }

        });

        document.getElementById('zoom_out').addEventListener('click', (e) => {
            if (myState.pdf == null) return;

            if (myState.zoom > 0.3) {
                myState.zoom = myState.zoom - 0.1;
                render();
            }

        });
    </script>

    <style>
        #my_pdf_viewer {
            width: 100%;
  
        }

        #canvas_container {
            width: 100%;
   
            overflow: auto;
            background-color: gray;
            text-align: center;
            border: 3px solid;
        }

        #pdf_renderer {
            border: none;
        }

        #navigation-controls {
            margin: 10px;
        }

        #navigation-controls button {
            background-color: #333;
            height: auto;
            line-height: 1;
            padding: 5px 8px;
        }

        #navigation-controls #zoom_out, #navigation-controls #zoom_in {
            padding: 5px 6px;
        }

        #navigation-controls input {
            color: #333;
            width: 50px;
            line-height: 1;
            padding: 2px;
            text-align: center;
        }
        i {
    font-size: 10px;
    color: white;
}
    </style>
    <script src="assets/nav.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
</body>

</html>