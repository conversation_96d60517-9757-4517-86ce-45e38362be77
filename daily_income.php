<?php
session_start();
error_reporting(0);
include('admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

// Check if today is Saturday (6) or Sunday (0)
$dayOfWeek = date('w'); // Sunday = 0, Monday = 1, ..., Saturday = 6
if ($dayOfWeek == 0 || $dayOfWeek == 6) {
	echo "Daily income not processed on weekends.";
	exit;
}

/**
 * Recursively calculate and distribute level income bonuses.
 * 
 * @param int    $upliner_id  The current upliner’s user ID.
 * @param int    $user_id     The user who earned the daily income.
 * @param float  $daily_income The daily income amount of the user.
 * @param int    $level       The current level in the upliner chain.
 * @param string $date        The current date/time stamp.
 */
function calculateLevelIncome($upliner_id, $user_id, $daily_income, $level, $date)
{
	// Get the level income percentage for the current level.
	$sel_level = dbQuery("SELECT level_income FROM tabl_level_income WHERE level='" . $level . "'");
	$res_level = dbFetchAssoc($sel_level);

	if (!$res_level) {
		return; // No level income defined for this level, stop recursion.
	}

	$percentage = $res_level['level_income'];
	$level_bonus = $daily_income * $percentage / 100;

	// Get the upliner's details.
	$sel_upliner = dbQuery("SELECT upliner_id, earning_limit FROM tabl_user WHERE id='" . $upliner_id . "'");
	$res_upliner = dbFetchAssoc($sel_upliner);
	if (!$res_upliner) {
		return; // Stop if no upliner is found.
	}

	// Check if the upliner is within their earning limit.
	$upliner_wallet = dbQuery("SELECT SUM(receive_amount) as total_earning FROM tabl_main_wallet WHERE user_id='" . $upliner_id . "'");
	$res_wallet = dbFetchAssoc($upliner_wallet);

	if ($res_wallet['total_earning'] < $res_upliner['earning_limit']) {
		// Insert level bonus into the upliner's wallet.
		dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $upliner_id . "', receive_amount='" . $level_bonus . "', type=1, type_id=8, to_id='" . $user_id . "', narration='Level Income Bonus Level $level', date_added='" . $date . "'");
	} else {
		return; // Stop if the earning limit is reached.
	}

	// Recursive call for the next upliner in the chain.
	calculateLevelIncome($res_upliner['upliner_id'], $user_id, $daily_income, $level + 1, $date);
}


// Main daily income processing loop.
$sel = dbQuery("SELECT tabl_user.*, tabl_plans.id as plan_id, tabl_plans.income as daily_income FROM tabl_user INNER JOIN tabl_plans ON tabl_user.membership_id=tabl_plans.id WHERE tabl_user.membership_id!=0");
while ($res = dbFetchAssoc($sel)) {
	$user_id = $res['id'];
	$plan_id = $res['plan_id'];

	echo "user: " . $user_id . " - plan: " . $plan_id . "<br>";

	// (Optional) Retrieve total earnings for this user if needed.
	$total_income = dbQuery("SELECT sum(receive_amount) as total_earning FROM tabl_main_wallet WHERE user_id='" . $user_id . "'");
	$res_total_income = dbFetchAssoc($total_income);

	$sel_plan = dbQuery("SELECT * FROM tabl_plans WHERE id='$plan_id'");
	$sel_plan_transaction = dbQuery("SELECT * FROM tabl_plan_transaction WHERE plan_id='$plan_id' AND user_id='$user_id' AND status='1' ORDER BY id DESC LIMIT 1");
	$res_plan_transaction = dbFetchAssoc($sel_plan_transaction);
	$res_plan = dbFetchAssoc($sel_plan);

	if ($res_plan && $res_plan_transaction) {
		$price = $res_plan_transaction['price'];
		$income_per = $res_plan['income'];

		// Calculate daily income based on the plan's percentage.
		$daily_income = $price * $income_per / 100;

		// Check earning limit for upliner
		$refer_wallet = dbQuery("SELECT SUM(receive_amount) as total_earning FROM tabl_main_wallet WHERE user_id='" . $user_id . "'");
		$res_wallet = dbFetchAssoc($refer_wallet);

		echo "daily_income: " . $daily_income . " <br>";
		echo "earning_limit: " . $res['earning_limit'] . " <br>";

		echo "total_earning: " . $res_wallet['total_earning'] . " <br>";

		if (($res_wallet['total_earning'] + $daily_income)  < $res['earning_limit']) {

			// Insert the daily income for the user.
			dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $user_id . "', receive_amount='" . $daily_income . "', type=1, type_id=3, narration='Daily Income', date_added='" . $date . "'");

			// Calculate level income for upliners.
			// Ensure the user has an upliner before proceeding.
			if (!empty($res['upliner_id'])) {
				calculateLevelIncome($res['upliner_id'], $user_id, $daily_income, 1, $date);
			}
		}
	}
}
