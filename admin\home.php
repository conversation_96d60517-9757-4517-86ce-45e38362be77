<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 1;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title><?php echo SITE; ?>- DashBoard</title>
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

</head>

<body class="alt-menu sidebar-noneoverflow">
    <?php include('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">
        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <div class="row layout-top-spacing">
                    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 col-12 layout-spacing" onClick="window.location.href='users.php'">
                        <div class="widget widget-account-invoice-two">
                            <div class="widget-content">
                                <div class="account-box">
                                    <div class="info">
                                        <div class="inv-title">
                                            <?php

                                            $sel_user = dbQuery("SELECT * FROM tabl_user");
                                            $total_user = dbNumRows($sel_user);
                                            ?>
                                            <h4 class="home_section">Total Team<br />
                                                <?php echo $total_user; ?>
                                            </h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 col-12 layout-spacing" onClick="window.location.href='active_users.php'">
                        <div class="widget widget-account-invoice-three">
                            <div class="widget-content">
                                <div class="account-box">
                                    <div class="info">
                                        <div class="inv-title">
                                            <?php

                                            $sel_user = dbQuery("SELECT * FROM tabl_user");
                                            $active_user = 0;
                                            $inactive_user = 0;
                                            while ($res_user = dbFetchAssoc($sel_user)) {
                                                if ($res_user['status'] == 1) {
                                                    $active_user += 1;
                                                } else {
                                                    $inactive_user += 1;
                                                }
                                            }
                                            ?>
                                            <h4 class="home_section">Active User<br />
                                                <?php echo $active_user; ?>
                                            </h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 col-12 layout-spacing" onClick="window.location.href='deactive_users.php'">
                        <div class="widget widget-account-invoice-six">
                            <div class="widget-content">
                                <div class="account-box">
                                    <div class="info">
                                        <div class="inv-title">
                                            <h4 class="home_section">De-Active User<br />
                                                <?php echo $inactive_user; ?>
                                            </h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 col-12 layout-spacing">
                        <div class="widget widget-account-invoice-five">
                            <div class="widget-content">
                                <div class="account-box">
                                    <div class="info">
                                        <div class="inv-title">
                                         <?php

                                            $sel_wallet = dbQuery("SELECT sum(receive_amount-paid_amount) as ttlbalance FROM tabl_admin_wallet");
                                            $res_wallet = dbFetchAssoc($sel_wallet);
                                            ?>
                                            <h4 class="home_section">Wallet Amount<br/>$ 
											<?php if ($res_wallet['ttlbalance'] != "") {
                                                echo $res_wallet['ttlbalance'];
                                            } else {
                                                echo '0.00';
                                            } ?>
											</h4>
                                        </div>
                                    </div>                                    
                                </div>
                            </div>
                        </div>
                    </div> -->


                    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 col-12 layout-spacing" onClick="window.location.href='plan_requested.php'">
                        <div class="widget widget-account-invoice-four">
                            <div class="widget-content">
                                <div class="account-box">
                                    <div class="info">
                                        <div class="inv-title">
                                            <h4 class="home_section">Activation Request<br />
                                                <?php $sel_plan = dbQuery("SELECT * FROM tabl_plan_transaction WHERE status=0");
                                                $request_no = dbNumRows($sel_plan);
                                                echo  $request_no;
                                                ?>
                                            </h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 col-12 layout-spacing" onClick="window.location.href='user_wallet.php'">
                        <div class="widget widget-account-invoice-two">
                            <div class="widget-content">
                                <div class="account-box">
                                    <div class="info">
                                        <div class="inv-title">
                                            <?php

                                            $sel_wallet_r = dbQuery("SELECT * FROM tabl_bank_details WHERE `status`='0'");
                                            $total_wallet_r = dbNumRows($sel_wallet_r);
                                            ?>
                                            <h4 class="home_section">Wallet Request<br />
                                                <?php echo $total_wallet_r; ?>
                                            </h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 col-12 layout-spacing" onClick="window.location.href='withdrawl.php'">
                        <div class="widget widget-account-invoice-four">
                            <div class="widget-content">
                                <div class="account-box">
                                    <div class="info">
                                        <div class="inv-title">
                                            <h4 class="home_section">Withdraw Request<br />
                                                <?php $sel_withdrawl = dbQuery("SELECT * FROM tabl_withdrawl WHERE `status`=0");
                                                $request_withdrawl_no = dbNumRows($sel_withdrawl);
                                                echo  $request_withdrawl_no;
                                                ?>
                                            </h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 col-12 layout-spacing" onClick="window.location.href='contactus.php'">
                        <div class="widget widget-account-invoice-three">
                            <div class="widget-content">
                                <div class="account-box">
                                    <div class="info">
                                        <div class="inv-title">
                                            <h4 class="home_section">Feedback<br />
                                                <?php $sel_contactus = dbQuery("SELECT * FROM tabl_contactus WHERE `status`=1");
                                                $num_contactus = dbNumRows($sel_contactus);
                                                echo  $num_contactus;
                                                ?>
                                            </h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>


            </div>
            <?php include('inc/__footer.php'); ?>
        </div>
    </div>
    <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        $(document).ready(function() {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    <script src="plugins/apex/apexcharts.min.js"></script>
    <script src="assets/js/dashboard/dash_2.js"></script>
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
</body>
<div class="modal fade" id="multiple_receive" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content"></div>
    </div>
</div>
<div class="modal fade" id="multiple_payment" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content"></div>
    </div>
</div>

</html>
<style>
    .widget.widget-one .widget-heading h6 {
        margin-bottom: 5px !important;
    }

    .widget.widget-one {
        padding: 16px 13px !important;
    }

    #search-list {
        float: left;
        list-style: none;
        margin: 0;
        padding: 0;
        width: 93%;
        position: absolute;
        font-family: inherit;
        border-right: 1px solid rgba(158, 149, 149, 0.21);
        z-index: 100;
    }

    #search-list li {
        padding: 10px;
        background: #463d3dc4;
        border-bottom: #F0F0F0 1px solid;
        color: white;
        border-radius: 6px;
        cursor: pointer;
    }

    .home_section {
        color: #FFF;
        text-align: center;

    }
</style>