<?php
session_start();
include('../lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');


if ($_REQUEST['val'] == 1) {

	$sel = dbQuery("SELECT * FROM tabl_add_cash WHERE id='" . $_REQUEST['d_id'] . "'");
	$res = dbFetchAssoc($sel);

	dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res['user_id'] . "',type=1,type_id=1,receive_amount='" . $res['amount'] . "',narration='Fund Request Completed',date_added='" . $date . "'");
}

dbQuery("UPDATE tabl_add_cash SET status='" . $_REQUEST['val'] . "' WHERE id='" . $_REQUEST['d_id'] . "'");
echo '1';
