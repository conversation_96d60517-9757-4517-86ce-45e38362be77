
#sidenav {
    max-height: 100%;
    height: 100%;
    max-width: 70vw;
    min-width: 300px;
    overflow-x: hidden;
    overflow-y: auto;
  
    transition: all .3s ease-in-out;
    transform: translate(-150%, 0px);
    -webkit-transform: translate(-150%, 0px);
    /* Safari 3-4, iOS 4.0.2 - 4.2, Android 2.3+ */
    -ms-transform: translate(-150%, 0px);
  
  }
  
  #sidenav.active {
    transition: all .3s ease-in-out;
    transform: translate(0%, 0px);
    -webkit-transform: translate(0%, 0px);
    -ms-transform: translate(0%, 0px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, .4);
  }
  
  #overlay {
    width: 100vw;
    height: 100vh;
    display: none;
    animation: fade 5s;
    -webkit-animation: fade 500ms;
    -moz-animation: fade 500ms;
    -o-animation: fade 500ms;
  }
  
  #overlay.active {
    display: block;
  }
  
  @keyframes fade {
    0% {
      opacity: 0;
    }
  
    100% {
      opacity: 1;
    }
  }
  
  @-moz-keyframes fade {
    0% {
      opacity: 0;
    }
  
    100% {
      opacity: 1;
    }
  }
  
  @-webkit-keyframes fade {
    0% {
      opacity: 0;
    }
  
    100% {
      opacity: 1;
    }
  }
  
  @-o-keyframes fade {
    0% {
      opacity: 0;
    }
  
    100% {
      opacity: 1;
    }
  }
  

  
  .burger span {
    display: block;
    width: 20px;
    height: 2px;
    border-radius: 3px;
  }
  
  .pointer {
    cursor: pointer;
  }
  
  .close {
    width: 23px;
    height: 23px;
  }
  
  .cross {
    height: 23px;
    width: 2px;
    border-radius: 3px;
  }
  
  .cross.left {
    transform: rotate(45deg);
  }
  
  .cross.right {
    transform: rotate(-45deg);
  }
  
  .align-middle {
    vertical-align: middle
  }
  .z4 {
    z-index: 4;
}
.left-0 {
    left: 0px;
}
.top-0 {
    top: 0px;
}
.fixed {
    position: fixed;
}
.p2 {
    padding: 1rem;
}.bg-darken-4 {
    background-color: rgba(0, 0, 0, 0.5);
}.z3 {
    z-index: 3;
}.close {
    width: 23px;
    height: 23px;
}
.pointer {
    cursor: pointer;
}
.relative {
    position: relative;
}

.justify-center {
    -webkit-box-pack: center;
    justify-content: center;
}
.items-center {
    -webkit-box-align: center;
    align-items: center;
}
.flex {
    display: flex;
}
.mb2 {
    margin-bottom: 1rem;
}
.right {
    float: right;
}
.m0 {
    margin: 0px;
}
.bold {
    font-weight: 700;
}hr {
    margin-top: 1.5em;
    margin-bottom: 1.5em;
    border-width: 0px 0px 1px;
    border-top-style: initial;
    border-right-style: initial;
    border-left-style: initial;
    border-top-color: initial;
    border-right-color: initial;
    border-left-color: initial;
    border-image: initial;
    border-bottom-style: solid;
    border-bottom-color: rgb(204, 204, 204);
}.list-reset {
    list-style: none;
    padding-left: 0px;
}.mb2 {
    margin-bottom: 1rem;
}
.caps {
    text-transform: uppercase;
    letter-spacing: 0.2em;
}
.h6 {
    font-size: 0.75rem;
}
.id {
  background: #fff1f1;
}
.image {
  height: 60px;
  width: 60px;
  background: white;
  border-radius: 50%;
}
.mr25 {
  margin-right: 25px !important;
}
.fontsize14 {
  font-size: 22px;
}
.fontweight500 {
  font-weight: 500;
}
.fontsize13 {
  font-size: 19px;
}
.fontsize15 {
  font-size: 20px;
}
.mt25 {
  margin-top: 25px !important;
}
.mb2 {
  margin-bottom: 1rem !important;
}
.ml-1 {
  margin-left: 15px !important;
}