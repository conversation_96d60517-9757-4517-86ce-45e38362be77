<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 3;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');

$user = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_REQUEST['id'] . "'");
$res_user = dbFetchAssoc($user);

$main_wallet = dbQuery("SELECT sum(receive_amount-paid_amount) as balance_amount FROM tabl_main_wallet WHERE user_id='" . $_REQUEST['id'] . "'");
$res_main_wallet = dbFetchAssoc($main_wallet);
if ($res_main_wallet['balance_amount'] != "") {
  $m_wallet = $res_main_wallet['balance_amount'];
} else {
  $m_wallet = '0.00';
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
  <title><?php echo SITE; ?>- Main Wallet</title>
  <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
  <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
  <script src="assets/js/loader.js"></script>

  <!-- BEGIN GLOBAL MANDATORY STYLES -->

  <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
  <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
  <!-- END GLOBAL MANDATORY STYLES -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->

  <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
  <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />

  <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

  <link rel="stylesheet" type="text/css" href="plugins/table/datatable/datatables.css">
  <link rel="stylesheet" type="text/css" href="plugins/table/datatable/dt-global_style.css">
  <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/regular.css">
  <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/fontawesome.css">
</head>

<body class="alt-menu sidebar-noneoverflow">
  <?php include('inc/__header.php'); ?>

  <!--  BEGIN MAIN CONTAINER  -->

  <div class="main-container" id="container">
    <div class="overlay"></div>
    <div class="search-overlay"></div>

    <!--  BEGIN TOPBAR  -->

    <?php include('inc/__menu.php'); ?>

    <!--  END TOPBAR  -->

    <!--  BEGIN CONTENT PART  -->

    <div id="content" class="main-content">
      <div class="layout-px-spacing">
        <nav class="breadcrumb-one" aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="home.php">Home</a></li>
            <li class="breadcrumb-item active"><a href="javascript:void(0);">Wallet Details</a></li>
          </ol>
        </nav>
        <div class="account-settings-container layout-top-spacing">
          <div class="account-content">
            <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
              <div class="row">
                <div class="col-xl-12 col-lg-12 col-sm-12  layout-spacing">
                  <div class="widget-content widget-content-area br-6">
                    <h4>Wallet Details </h4>
                    <div class="row">
                      <div class="col-md-3">
                        <h5>SponsorID: <span style="color:#096;"><?php echo $res_user['sponsor_id']; ?></span></h5>
                      </div>
                      <div class="col-md-3">
                        <h5>Total Balance: <span style="color:#096;"><?= $currencySymbols; ?> <?php echo $m_wallet; ?></span></h5>
                      </div>
                    </div>



                    <div class="table-responsive mb-4 mt-4">
                      <table id="zero-config" class="table table-hover" style="width:100%">
                        <thead>
                          <tr>
                            <th>S.No</th>
                            <th>Date</th>
                            <th>Purpose</th>
                            <th>Received</th>
                            <th>Paid</th>
                          </tr>
                        </thead>
                        <tbody>
                          <?php

                          $sel = dbQuery("SELECT tabl_main_wallet.*,tabl_user.name,tabl_user.sponsor_id FROM tabl_main_wallet INNER JOIN tabl_user ON tabl_main_wallet.user_id=tabl_user.id WHERE tabl_main_wallet.user_id='" . $_REQUEST['id'] . "' ORDER BY tabl_main_wallet.id DESC");
                          $i = 1;
                          while ($res = dbFetchAssoc($sel)) { ?>
                            <tr>
                              <td><?php echo $i; ?></td>
                              <td><?php echo date('d-m-Y H:i:s', strtotime($res['date_added'])); ?></td>
                              <td><?php if ($res['type'] == 2) {
                                    echo $res['narration'];
                                  } else {
                                    echo $res['narration'] . '';
                                  }
                                  ?></td>
                              <td><?php echo $res['receive_amount']; ?></td>
                              <td><?php echo $res['paid_amount']; ?></td>
                            </tr>
                          <?php $i++;
                          } ?>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <?php include('inc/__footer.php'); ?>
      </div>
    </div>

    <!--  END CONTENT PART  -->

  </div>

  <!-- END MAIN CONTAINER -->

  <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->

  <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
  <script src="bootstrap/js/popper.min.js"></script>
  <script src="bootstrap/js/bootstrap.min.js"></script>
  <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
  <script src="assets/js/app.js"></script>
  <script>
    $(document).ready(function() {

      App.init();

    });
  </script>
  <script src="assets/js/custom.js"></script>

  <!-- END GLOBAL MANDATORY SCRIPTS -->

  <!-- BEGIN PAGE LEVEL SCRIPTS -->

  <script src="plugins/table/datatable/datatables.js"></script>
  <script>
    $('#zero-config').DataTable({

      "oLanguage": {

        "oPaginate": {
          "sPrevious": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>',
          "sNext": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>'
        },

        "sInfo": "Showing page _PAGE_ of _PAGES_",

        "sSearch": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',

        "sSearchPlaceholder": "Search...",

        "sLengthMenu": "Results :  _MENU_",

      },

      "stripeClasses": [],

      "lengthMenu": [8, 10, 20, 50],

      "pageLength": 10

    });
  </script>

  <!-- END PAGE LEVEL SCRIPTS -->
</body>
<script>
  function isNumber(evt) {
    var iKeyCode = (evt.which) ? evt.which : evt.keyCode
    if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
      return false;

    return true;
  }

  function isDecimal(evt, obj) {

    var charCode = (evt.which) ? evt.which : event.keyCode
    var value = obj.value;
    var dotcontains = value.indexOf(".") != -1;
    if (dotcontains)
      if (charCode == 46) return false;
    if (charCode == 46) return true;
    if (charCode > 31 && (charCode < 48 || charCode > 57))
      return false;
    return true;
  }
</script>

<script>
  function set_w_status(val, w_id) {
    var retVal = confirm("Are you sure want to change withdrawl status.");
    if (retVal == true) {
      $.ajax({
        url: 'ajax/change_withdraw_status.php',
        type: 'post',
        data: {
          'val': val,
          'w_id': w_id
        },
        success: function(data) {
          //alert(data);
          if (data == 1) {
            location.reload();
          }
        },
      });
    } else {
      return false;
    }
  }
</script>

</html>
<style>
  * {
    box-sizing: border-box;
  }

  body {
    font: 16px Arial;
  }

  /*the container must be positioned relative:*/
  .autocomplete {
    position: relative;
    display: inline-block;
  }

  input {
    border: 1px solid transparent;
    background-color: #f1f1f1;
    padding: 10px;
    font-size: 16px;
  }

  input[type=text] {
    background-color: #f1f1f1;
    width: 100%;
  }

  input[type=submit] {
    background-color: DodgerBlue;
    color: #fff;
    cursor: pointer;
  }

  .autocomplete-items {
    position: absolute;
    border: 1px solid #d4d4d4;
    border-bottom: none;
    border-top: none;
    z-index: 99;
    /*position the autocomplete items to be the same width as the container:*/
    top: 100%;
    left: 0;
    right: 0;
  }

  .autocomplete-items div {
    padding: 10px;
    cursor: pointer;
    background-color: #fff;
    border-bottom: 1px solid #d4d4d4;
  }

  /*when hovering an item:*/
  .autocomplete-items div:hover {
    background-color: #e9e9e9;
  }

  /*when navigating through the items using the arrow keys:*/
  .autocomplete-active {
    background-color: DodgerBlue !important;
    color: #ffffff;
  }

  .note {
    font-size: 12px;
    font-weight: bold;
    color: #000;
  }

  .note span {
    color: #F00;
  }

  .msg_with {
    color: #060 !important;
  }
</style>
<script>
  function autocomplete(inp, arr) {
    /*the autocomplete function takes two arguments,
    the text field element and an array of possible autocompleted values:*/
    var currentFocus;
    /*execute a function when someone writes in the text field:*/
    inp.addEventListener("input", function(e) {
      var a, b, i, val = this.value;
      /*close any already open lists of autocompleted values*/
      closeAllLists();
      if (!val) {
        return false;
      }
      currentFocus = -1;
      /*create a DIV element that will contain the items (values):*/
      a = document.createElement("DIV");
      a.setAttribute("id", this.id + "autocomplete-list");
      a.setAttribute("class", "autocomplete-items");
      /*append the DIV element as a child of the autocomplete container:*/
      this.parentNode.appendChild(a);
      /*for each item in the array...*/
      for (i = 0; i < arr.length; i++) {
        /*check if the item starts with the same letters as the text field value:*/
        if (arr[i].substr(0, val.length).toUpperCase() == val.toUpperCase()) {
          /*create a DIV element for each matching element:*/
          b = document.createElement("DIV");
          /*make the matching letters bold:*/
          b.innerHTML = "<strong>" + arr[i].substr(0, val.length) + "</strong>";
          b.innerHTML += arr[i].substr(val.length);
          /*insert a input field that will hold the current array item's value:*/
          b.innerHTML += "<input type='hidden' value='" + arr[i] + "'>";
          /*execute a function when someone clicks on the item value (DIV element):*/
          b.addEventListener("click", function(e) {
            /*insert the value for the autocomplete text field:*/
            inp.value = this.getElementsByTagName("input")[0].value;
            /*close the list of autocompleted values,
            (or any other open lists of autocompleted values:*/
            closeAllLists();
          });
          a.appendChild(b);
        }
      }
    });
    /*execute a function presses a key on the keyboard:*/
    inp.addEventListener("keydown", function(e) {
      var x = document.getElementById(this.id + "autocomplete-list");
      if (x) x = x.getElementsByTagName("div");
      if (e.keyCode == 40) {
        /*If the arrow DOWN key is pressed,
        increase the currentFocus variable:*/
        currentFocus++;
        /*and and make the current item more visible:*/
        addActive(x);
      } else if (e.keyCode == 38) { //up
        /*If the arrow UP key is pressed,
        decrease the currentFocus variable:*/
        currentFocus--;
        /*and and make the current item more visible:*/
        addActive(x);
      } else if (e.keyCode == 13) {
        /*If the ENTER key is pressed, prevent the form from being submitted,*/
        e.preventDefault();
        if (currentFocus > -1) {
          /*and simulate a click on the "active" item:*/
          if (x) x[currentFocus].click();
        }
      }
    });

    function addActive(x) {
      /*a function to classify an item as "active":*/
      if (!x) return false;
      /*start by removing the "active" class on all items:*/
      removeActive(x);
      if (currentFocus >= x.length) currentFocus = 0;
      if (currentFocus < 0) currentFocus = (x.length - 1);
      /*add class "autocomplete-active":*/
      x[currentFocus].classList.add("autocomplete-active");
    }

    function removeActive(x) {
      /*a function to remove the "active" class from all autocomplete items:*/
      for (var i = 0; i < x.length; i++) {
        x[i].classList.remove("autocomplete-active");
      }
    }

    function closeAllLists(elmnt) {
      /*close all autocomplete lists in the document,
      except the one passed as an argument:*/
      var x = document.getElementsByClassName("autocomplete-items");
      for (var i = 0; i < x.length; i++) {
        if (elmnt != x[i] && elmnt != inp) {
          x[i].parentNode.removeChild(x[i]);
        }
      }
    }
    /*execute a function when someone clicks in the document:*/
    document.addEventListener("click", function(e) {
      closeAllLists(e.target);
    });
  }

  /*An array containing all the country names in the world:*/
  var countries = ["500", "1000", "1500", "2000", "3000", "5000", "10000", "25000", "50000"];

  /*initiate the autocomplete function on the "myInput" element, and pass along the countries array as possible autocomplete values:*/
  autocomplete(document.getElementById("myInput"), countries);
</script>
<script>
  function save_remark(w_id) {
    var remark = $("#remark_" + w_id).val();
    if (remark != "") {
      $.ajax({
        url: 'ajax/save_remark.php',
        type: 'post',
        data: {
          'remark': remark,
          'w_id': w_id
        },
        success: function(data) {
          if (data == 1) {
            $("#remark_msg_" + w_id).html("Remark Added!")
          }
        },
      });
    }

  }
</script>