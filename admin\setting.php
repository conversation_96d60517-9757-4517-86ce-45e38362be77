<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 101;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
if (isset($_REQUEST['submit'])) {
  $upd = dbQuery("UPDATE tabl_setting SET site_name='" . $_REQUEST['site_name'] . "',site_email='" . $_REQUEST['site_email'] . "' ,m_withdraw='" . $_REQUEST['m_withdraw'] . "',w_charges='" . $_REQUEST['w_charges'] . "',s_charges='" . $_REQUEST['s_charges'] . "',trc_address='" . mysqli_real_escape_string($con, $_REQUEST['trc_address']) . "',usdt_to_inr_rate='" . $_REQUEST['usdt_to_inr_rate'] . "',withdraw_from='" . $_REQUEST['withdraw_from'] . "',withdraw_to='" . $_REQUEST['withdraw_to'] . "' WHERE id='1'");

  echo '<script>alert("Setting Updated!");window.location.href="setting.php"</script>';
}

$sel = dbQuery("SELECT * FROM tabl_setting WHERE id='1'");
$res = dbFetchAssoc($sel);

?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
  <title><?php echo SITE; ?>- Setting</title>
  <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
  <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
  <script src="assets/js/loader.js"></script>
  <script src="https://cdn.ckeditor.com/4.22.1/standard/ckeditor.js"></script>

  <!-- BEGIN GLOBAL MANDATORY STYLES -->
  <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
  <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
  <!-- END GLOBAL MANDATORY STYLES -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
  <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
  <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
  <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

  <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
  <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
</head>

<body class="alt-menu sidebar-noneoverflow">
  <?php include('inc/__header.php'); ?>
  <!--  BEGIN MAIN CONTAINER  -->
  <div class="main-container" id="container">
    <div class="overlay"></div>
    <div class="search-overlay"></div>

    <!--  BEGIN TOPBAR  -->
    <?php include('inc/__menu.php'); ?>
    <!--  END TOPBAR  -->

    <!--  BEGIN CONTENT PART  -->
    <div id="content" class="main-content">
      <div class="layout-px-spacing">
        <nav class="breadcrumb-one" aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="home.php">Home</a></li>
            <li class="breadcrumb-item active"><a href="javascript:void(0);">Setting</a></li>
          </ol>
        </nav>
        <div class="account-settings-container layout-top-spacing">
          <div class="account-content">
            <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
              <div class="row">
                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                  <form name="account" method="post" id="general-info" class="section general-info" enctype="multipart/form-data">
                    <div class="info">
                      <h6 class="">Setting</h6>
                      <div class="row">
                        <div class="col-lg-12 mx-auto">
                          <div class="row">
                            <div class="col-xl-12 col-lg-12 col-md-8 mt-md-0 mt-4">
                              <div class="form-row mb-4">
                                <div class="form-group col-md-6">
                                  <label for="inputEmail4"><strong>Site Name</strong></label>
                                  <input type="text" class="form-control mb-4" id="Name" name="site_name" placeholder="Site Name" value="<?php echo $res['site_name']; ?>" required>
                                </div>
                                <div class="form-group col-md-6">
                                  <label for="inputEmail4"><strong>Site Email</strong></label>
                                  <input type="email" class="form-control mb-4" id="Name" name="site_email" placeholder="Site Email" value="<?php echo $res['site_email']; ?>" required>
                                </div>

                                <div class="form-group col-md-4">
                                  <label for="inputEmail4"><strong>Minimum Withdraw</strong></label>
                                  <input type="text" class="form-control mb-4" id="Name" onkeypress="return isDecimal(event,this)" name="m_withdraw" placeholder="Minimum Withdraw" value="<?php echo $res['m_withdraw']; ?>" required>
                                </div>

                                <div class="form-group col-md-4">
                                  <label for="inputEmail4"><strong>Withdraw Charges(%)</strong></label>
                                  <input type="text" class="form-control mb-4" id="Name" name="w_charges" placeholder="Withdraw Charges" value="<?php echo $res['w_charges']; ?>" onkeypress="return isDecimal(event,this)" required>
                                </div>

                                <div class="form-group col-md-4">
                                  <!-- <label for="inputEmail4"><strong>Service Charges(%)</strong></label> -->
                                  <label for="inputEmail4"><strong>Service Charges(<?= $currencySymbols; ?>)</strong></label>
                                  <input type="text" class="form-control mb-4" id="Name" name="s_charges" placeholder="Service Charges" value="<?php echo $res['s_charges']; ?>" onkeypress="return isDecimal(event,this)" required>
                                </div>

                                <div class="form-group col-md-6">
                                  <label for="withdraw_from">Withdraw From</label>
                                  <input type="time" class="form-control mb-4"
                                    id="withdraw_from" name="withdraw_from"
                                    placeholder="withdraw_from"
                                    value="<?php echo $res['withdraw_from']; ?>">
                                </div>

                                <div class="form-group col-md-6">
                                  <label for="withdraw_to">Withdraw To</label>
                                  <input type="time" class="form-control mb-4"
                                    id="withdraw_to" name="withdraw_to"
                                    placeholder="withdraw_to"
                                    value="<?php echo $res['withdraw_to']; ?>">
                                </div>


                                <div class="form-group col-md-4">
                                  <label for="usdt_to_inr_rate"><strong>USDT to INR Rate</strong></label>
                                  <input type="text" class="form-control mb-4" id="usdt_to_inr_rate" name="usdt_to_inr_rate" placeholder="USDT to INR Rate" value="<?php echo $res['usdt_to_inr_rate']; ?>" required>
                                </div>

                                <div class="form-group col-md-8">
                                  <label for="inputEmail4"><strong>BEP20 Address</strong></label>
                                  <input type="text" class="form-control mb-4" id="Name" name="trc_address" placeholder="BEP20 Address" value="<?php echo $res['trc_address']; ?>" required>
                                </div>



                              </div>
                              <div class="form">
                                <div class="row">
                                  <div class="col-sm-12">
                                    <div class="form-group">
                                      <label for="fullName"></label>
                                      <button type="submit" name="submit" class="btn btn-secondary mb-4 mr-2">Submit</button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
        <?php include('inc/__footer.php'); ?>
      </div>
    </div>
    <!--  END CONTENT PART  -->

  </div>
  <!-- END MAIN CONTAINER -->

  <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
  <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
  <script src="bootstrap/js/popper.min.js"></script>
  <script src="bootstrap/js/bootstrap.min.js"></script>
  <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
  <script src="assets/js/app.js"></script>
  <script>
    $(document).ready(function() {
      App.init();
    });
  </script>
  <script src="assets/js/custom.js"></script>
  <!-- END GLOBAL MANDATORY SCRIPTS -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
  <script src="plugins/apex/apexcharts.min.js"></script>
  <script src="assets/js/dashboard/dash_2.js"></script>
  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

  <script src="plugins/dropify/dropify.min.js"></script>
  <script src="plugins/blockui/jquery.blockUI.min.js"></script>
  <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
  <script src="assets/js/users/account-settings.js"></script>
</body>

</html>
<script>
  function isNumber(evt) {
    var iKeyCode = (evt.which) ? evt.which : evt.keyCode
    if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
      return false;

    return true;
  }

  function isDecimal(evt, obj) {

    var charCode = (evt.which) ? evt.which : event.keyCode
    var value = obj.value;
    var dotcontains = value.indexOf(".") != -1;
    if (dotcontains)
      if (charCode == 46) return false;
    if (charCode == 46) return true;
    if (charCode > 31 && (charCode < 48 || charCode > 57))
      return false;
    return true;
  }
</script>