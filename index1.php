<?php
@session_start();
include('./lib/auth.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');


$sel = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
$res = dbFetchAssoc($sel);

// echo $res['id'];
$wallet_balance = dbQuery("SELECT sum(receive_amount-paid_amount) as ttlamount FROM tabl_main_wallet WHERE user_id='" . $res['id'] . "'");
$res_wallet_amount = dbFetchAssoc($wallet_balance);

?>


<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Home</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <link rel="stylesheet" href="assets/nav.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css">
    <style>
        .popup-link {
            display: flex;
            flex-wrap: wrap;
        }

        .popup-link a {
            background: #333;
            color: #fff;
            padding: 10px 30px;
            border-radius: 5px;
            font-size: 17px;
            cursor: pointer;
            margin: 20px;
            text-decoration: none;
        }

        .popup-container {
            visibility: hidden;
            opacity: 0;
            transition: all 0.3s ease-in-out;
            transform: scale(1.3);
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(21, 17, 17, 0.61);
            display: flex;
            align-items: center;
        }

        .popup-content {
            background-color: #fefefe;
            margin: auto;
            padding: 12px;
            padding: -21px;
            border: 1px solid #888;
            width: 80%;
        }

        .popup-content p {
            font-size: 17px;
            padding: 10px;
            line-height: 20px;
        }

        .popup-content a.close {
            color: #aaaaaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            background: none;
            padding: 0;
            margin: 0;
            text-decoration: none;
        }

        .popup-content a.close:hover {
            color: #333;
        }

        .popup-content span:hover,
        .popup-content span:focus {
            color: #000;
            text-decoration: none;
            cursor: pointer;
        }

        .popup-container:target {
            visibility: visible;
            opacity: 1;
            transform: scale(1);
        }

        .popup-container h3 {
            margin: 10px;
        }

        .home img {
            vertical-align: middle;
            width: 21%;
            border-radius: 50%;
            height: 98%;
        }

        .header {
            background: #823fb5;
            padding: 1px;
        }

        .name {
            color: #fff;
            margin-left: 9px;
            line-height: 24px;
        }

        .name p {
            font-size: 13px;
        }

        .refresh p {
            font-size: 16px;
            margin-bottom: 21px;
        }

        .deposit-name p {
            margin: auto;
        }
    </style>
</head>

<body>
    <div class="fullbody">
        <div class="header">
            <div class="header-items d-flex align-items-center justify-content-between m-2 row">
                <!-- <div class="col-2 d-flex justify-content-start"> -->
                <!-- <div class="home"> -->
                <!-- <h4>Home</h4> -->
                <!-- <div class="logo1 position-relative burger"> -->
                <!-- <img src="assets/image/Create-your-company-logo-with-our-online-logo-design-systems.png" alt=""> -->
                <!-- <div class="hamber d-flex justify-content-center align-items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-list" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z"/>
                  </svg>
                </div> -->
                <!-- </div>
                    </div>
                </div> -->
                <div class="col-10 d-flex justify-content-start">
                    <div class="home d-flex">
                        <img src="assets/image/profile_pic/<?php echo $user['user_image']; ?>" alt="">
                        <div class="name">
                            <p class="mb-0" style="font-size: 19px;"><?php echo $user['name']; ?></p>
                            <p class="mb-0"><?php echo $user['sponsor_id']; ?></p>
                        </div>

                    </div>
                </div>
                <div class="col-2 d-flex justify-content-end ">
                    <div class="web">
                        <!-- <i class="bi bi-globe"></i> -->
                    </div>

                    <!-- <div class="web">
                        <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="white" class="bi bi-telegram" viewBox="0 0 16 16">
                            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.287 5.906c-.778.324-2.334.994-4.666 2.01-.378.15-.577.298-.595.442-.03.243.275.339.69.47l.175.055c.408.133.958.288 1.243.294.26.006.549-.1.868-.32 2.179-1.471 3.304-2.214 3.374-2.23.05-.012.12-.026.166.016.047.041.042.12.037.141-.03.129-1.227 1.241-1.846 1.817-.193.18-.33.307-.358.336a8.154 8.154 0 0 1-.188.186c-.38.366-.664.64.015 1.088.327.216.589.393.85.571.284.194.568.387.936.629.093.06.183.125.27.187.331.236.63.448.997.414.214-.02.435-.22.547-.82.265-1.417.786-4.486.906-5.751a1.426 1.426 0 0 0-.013-.315.337.337 0 0 0-.114-.217.526.526 0 0 0-.31-.093c-.3.005-.763.166-2.984 1.09z"/>
                        </svg>
                    </div> -->

                    <!-- <div class="web" onclick="window.location.href = 'about.html'">
            
                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="white" class="bi bi-info-circle-fill" viewBox="0 0 16 16">
                        <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
                    </svg>
                    </div> -->
                    <div class="web" data-bs-toggle="modal" data-bs-target="#exampleModal">
                        <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="white" class="bi bi-translate" viewBox="0 0 16 16">
                            <path d="M4.545 6.714 4.11 8H3l1.862-5h1.284L8 8H6.833l-.435-1.286H4.545zm1.634-.736L5.5 3.956h-.049l-.679 2.022H6.18z" />
                            <path d="M0 2a2 2 0 0 1 2-2h7a2 2 0 0 1 2 2v3h3a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-3H2a2 2 0 0 1-2-2V2zm2-1a1 1 0 0 0-1 1v7a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H2zm7.138 9.995c.193.301.402.583.63.846-.748.575-1.673 1.001-2.768 1.292.178.217.451.635.555.867 1.125-.359 2.08-.844 2.886-1.494.777.665 1.739 1.165 2.93 1.472.133-.254.414-.673.629-.89-1.125-.253-2.057-.694-2.82-1.284.681-.747 1.222-1.651 1.621-2.757H14V8h-3v1.047h.765c-.318.844-.74 1.546-1.272 2.13a6.066 6.066 0 0 1-.415-.492 1.988 1.988 0 0 1-.94.31z" />
                        </svg>

                    </div>
                    <div>

                    </div>
                    <script src="https://translate.google.com/translate_a/element.js?cb=loadGoogleTranslate"></script>
                    <script>
                        function loadGoogleTranslate() {
                            new google.translate.TranslateElement("google_element");
                        }
                    </script>


                </div>




            </div>
        </div>


        <!-- <div class="task m-3">
            <h4>
                Newsroom
            </h4>
        </div> -->
        <!-- <div class="logo d-flex align-items-center">
            <div class="logo1" style="width: 60px;">
                <img src="assets/image/template_0.jpg" alt="">
            </div>
            <div class="client-name d-flex justify-content-between w-100">
                <div class="name">
                    <p class="mb-0" style="font-size: 19px;">Rohan</p>
                    <p class="mb-0">Username</p>
                </div>
                
            </div>
                   <div class="web">
                  
                    <i class="bi bi-arrow-clockwise" style="color: black;"></i>
                </div> 
        </div> -->




        <!-- <div class="task m-3">
            <h4>
                News
            </h4>
        </div> -->

        <!-- <div class="logo d-flex align-items-center " style="padding-bottom: 100px;">
            <?php
            $sel_news = dbQuery("SELECT * FROM tabl_news ORDER BY id DESC");
            $i = 1;
            while ($res_news = dbFetchAssoc($sel_news)) {
            ?>
                <div class="row">
                    <div class="col-12">
                        <p><b><?php echo $res_news['title']; ?></b></p>
                        <p><?php echo $res_news['description']; ?></p>

                        <?php if ($res_news['video']) { ?>
                            <iframe width="100%" height="200" src="./assets/image/news/<?php echo $res_news['video']; ?>" frameborder="0" allowfullscreen></iframe>
                        <?php  }
                        if ($res_news['image']) { ?>
                            <img src="assets/image/news/<?php echo $res_news['image']; ?>" alt="" style="width: 100%">
                        <?php } ?>
                    </div>
                </div>
            <?php
            }
            ?>
        </div> -->



        <div class="logo">
            <div class="assets d-flex justify-content-between align-items-center mb-2">

                <div class="assets d-flex justify-content-between  flex-column">
                    <p>My total assets</p>
                    <div class="assetsprice d-flex justify-content-between">
                        <p>
                            <?php

                            $price = 0;
                            $sel_plan = dbQuery("SELECT * FROM tabl_plan_transaction WHERE user_id='" . $res['id'] . "'");
                            if ($res_plan = dbFetchAssoc($sel_plan)) {
                                $price = $res_plan['price'];
                            }


                            if ($res_wallet_amount['ttlamount'] == "") {
                                echo $price;
                            } else {
                                echo $price + $res_wallet_amount['ttlamount'];
                            }
                            ?>
                        </p>
                    </div>
                </div>

            </div>

            <div class="profits d-flex justify-content-between align-items-center">
                <div class="today d-flex flex-column">
                    <p>Today's profits</p>
                    <p>
                        <?php
                        $today_income = dbQuery("SELECT sum(receive_amount) as ttlreceive FROM tabl_main_wallet WHERE user_id='" . $res['id'] . "' AND type=1 AND type_id=3 AND DATE(date_added)=DATE(NOW())");
                        $res_today_income = dbFetchAssoc($today_income);
                        if ($res_today_income['ttlreceive'] == "") {
                            echo '0.00';
                        } else {
                            echo $res_today_income['ttlreceive'];
                        }
                        ?>
                    </p>
                </div>
                <div class="today d-flex flex-column">
                    <p>Promotion bonus</p>
                    <p>
                        <?php
                        $promotion_income = dbQuery("SELECT sum(receive_amount) as ttlreceive FROM tabl_main_wallet WHERE user_id='" . $res['id'] . "' AND type=1 AND (type_id=2 OR type_id=4)");
                        $res_promotion_income = dbFetchAssoc($promotion_income);
                        if ($res_promotion_income['ttlreceive'] == "") {
                            echo '0.00';
                        } else {
                            echo $res_promotion_income['ttlreceive'];
                        }
                        ?>
                    </p>
                </div>
                <div class="today d-flex flex-column">
                    <p>Accumulated profits</p>
                    <p>
                        <?php
                        $total_income = dbQuery("SELECT sum(receive_amount) as ttlreceive FROM tabl_main_wallet WHERE user_id='" . $res['id'] . "' AND type=1");
                        $res_total_income = dbFetchAssoc($total_income);
                        if ($res_total_income['ttlreceive'] == "") {
                            echo '0.00';
                        } else {
                            echo $res_total_income['ttlreceive'];
                        }
                        ?>
                    </p>
                </div>
            </div>
        </div>


        <?php $news = dbQuery("SELECT * FROM tabl_news ORDER BY id DESC");
        $num_news = dbNumRows($news);
        if ($num_news > 0) { ?>
            <div class="task m-3">
                <h4> Newsroom </h4>
            </div>

            <div class="slider-container">
                <?php
                $i = 1;
                while ($res_news = dbFetchAssoc($news)) { ?>
                    <div class="slide active" id="slide<?php echo $i; ?>">
                        <div class="logo d-flex align-items-center">
                            <div class="client-name d-flex justify-content-between w-100 flex-column">
                                <!-- <div class="name">
                                    <p class="mb-0"><?php echo $res_news['title']; ?></p>
                                </div>
                                <div class="refresh">
                                    <p><?php echo $res_news['description']; ?></p>
                                </div> -->
                                <p><b><?php echo $res_news['title']; ?></b></p>
                                <p><?php echo $res_news['description']; ?></p>

                                <?php if ($res_news['image'] != "") { ?>
                                    <div class="imge">
                                        <div class="news_img" style="background-image: url('assets/image/news/<?php echo $res_news['image'] ?>'"></div>
                                        <!-- <img class="w-100 mt-2" src="assets/image/news/<?php echo $res_news['image'] ?>"> -->
                                    </div>

                                <?php } ?>
                                <?php if ($res_news['video'] != "") { ?>
                                    <div class="imge">
                                        <!-- <video width="200" height="100" controls=""> -->
                                        <video controls="">
                                            <source src="assets/image/news/<?php echo $res_news['video'] ?>" type="video/mp4">
                                            <source src="assets/image/news/<?php echo $res_news['video'] ?>" type="video/ogg">
                                            Your browser does not support the video tag.
                                        </video>
                                    </div>
                                <?php
                                } ?>
                            </div>
                        </div>
                    </div>
                <?php $i++;
                } ?>
                <i class="arrow fa-solid fa-arrow-left" id="prevBtn"></i>
                <i class="arrow fa-solid fa-arrow-right" id="nextBtn"></i>
            </div>
        <?php    } ?>


        <!-- <div class="d-flex">
            <div class="logo d-flex align-items-center w-50 mb-0 mr-0">
                <div class="logo1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#009688" class="bi bi-info-circle-fill" viewBox="0 0 16 16">
                        <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
                      </svg>
                </div>
                <div class="client-name d-flex justify-content-center w-100">
                    <div class="name">
                        <p class="mb-0">About Us</p>
                    </div>
                  
                </div>
            </div>
            <div class="logo d-flex align-items-center w-50 mb-0">
                <div class="logo1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#ff5722" class="bi bi-headset" viewBox="0 0 16 16">
                        <path d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5z"/>
                      </svg>
                </div>
                <div class="client-name d-flex justify-content-center w-100">
                    <div class="name">
                        <p class="mb-0">Customer Service</p>
                    </div>
                  
                </div>
            </div>
        </div> -->


        <div class="hello">
            <div class="helloimg">
                <img src="assets/image/brush-stroke-watercolor-infographic-template-presentation-slide-hello-banner-creative-art-concept-elements-148957111.jpg" alt="">
            </div>
        </div>

        <!-- <div class="d-flex">
            <div class="logo d-flex align-items-center w-50 mb-0 mr-0">
                <div class="logo1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#F44336" class="bi bi-download" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                        <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
                      </svg>
                </div>
                <div class="client-name d-flex justify-content-center w-100">
                    <div class="name">
                        <p class="mb-0">Deposit</p>
                    </div>
                  
                </div>
            </div>
            <div class="logo d-flex align-items-center w-50 mb-0" onclick="window.location.href = 'claim.html'">
                <div class="logo1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#FF9800" class="bi bi-gift" viewBox="0 0 16 16">
                        <path d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A2.968 2.968 0 0 1 3 2.506V2.5zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43a.522.522 0 0 0 .023.07zM9 3h2.932a.56.56 0 0 0 .023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0V3zM1 4v2h6V4H1zm8 0v2h6V4H9zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5V7zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5H7z"/>
                      </svg>
                </div>
                <div class="client-name d-flex justify-content-center w-100">
                    <div class="name">
                        <p class="mb-0">Claim</p>
                    </div>
                  
                </div>
            </div>
        </div> -->
        <!-- <div class="d-flex"onclick="window.location.href = 'withdraw.html'">
            <div class="logo d-flex align-items-center w-50 mb-0 mr-0">
                <div class="logo1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#009688" class="bi bi-arrow-up-right" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M14 2.5a.5.5 0 0 0-.5-.5h-6a.5.5 0 0 0 0 1h4.793L2.146 13.146a.5.5 0 0 0 .708.708L13 3.707V8.5a.5.5 0 0 0 1 0v-6z"/>
                      </svg>
                </div>
                <div class="client-name d-flex justify-content-center w-100">
                    <div class="name">
                        <p class="mb-0">Withdraw</p>
                    </div>
                  
                </div>
            </div>
            <div class="logo d-flex align-items-center w-50 mb-0">
                <div class="logo1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#E91E63" class="bi bi-wallet2" viewBox="0 0 16 16">
                        <path d="M12.136.326A1.5 1.5 0 0 1 14 1.78V3h.5A1.5 1.5 0 0 1 16 4.5v9a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 0 13.5v-9a1.5 1.5 0 0 1 1.432-1.499L12.136.326zM5.562 3H13V1.78a.5.5 0 0 0-.621-.484L5.562 3zM1.5 4a.5.5 0 0 0-.5.5v9a.5.5 0 0 0 .5.5h13a.5.5 0 0 0 .5-.5v-9a.5.5 0 0 0-.5-.5h-13z"/>
                      </svg>
                </div>
                <div class="client-name d-flex justify-content-center w-100">
                    <div class="name">
                        <p class="mb-0">Wallet</p>
                    </div>
                  
                </div>
            </div>
        </div> -->

        <div class="py-3">
            <div class="deposit-row container-fluid my-0">
                <div class="d-flex justify-content-between  row">
                    <div class="deposit d-flex flex-column align-items-center col-4">
                        <!-- <a href="deposit.php"> -->
                        <a href="plan.php">
                            <div class="icons">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                                    <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z" />
                                    <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z" />
                                </svg>
                            </div>
                        </a>
                        <div class="deposit-name">
                            <p>Deposit</p>
                        </div>
                    </div>
                    <div class="deposit d-flex flex-column align-items-center col-4">
                        <a href="withdraw.php">
                            <div class="icons1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-up-right" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M14 2.5a.5.5 0 0 0-.5-.5h-6a.5.5 0 0 0 0 1h4.793L2.146 13.146a.5.5 0 0 0 .708.708L13 3.707V8.5a.5.5 0 0 0 1 0v-6z" />
                                </svg>
                            </div>
                        </a>
                        <div class="deposit-name">
                            <p>Withdraw</p>
                        </div>
                    </div>
                    <div class="deposit d-flex flex-column align-items-center col-4">
                        <div class="icons2">
                            <a href="#popup1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-people" viewBox="0 0 16 16">
                                    <path d="M15 14s1 0 1-1-1-4-5-4-5 3-5 4 1 1 1 1zm-7.978-1A.261.261 0 0 1 7 12.996c.001-.264.167-1.03.76-1.72C8.312 10.629 9.282 10 11 10c1.717 0 2.687.63 3.24 1.276.593.69.758 1.457.76 1.72l-.008.002a.274.274 0 0 1-.014.002H7.022ZM11 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4m3-2a3 3 0 1 1-6 0 3 3 0 0 1 6 0M6.936 9.28a5.88 5.88 0 0 0-1.23-.247A7.35 7.35 0 0 0 5 9c-4 0-5 3-5 4 0 .667.333 1 1 1h4.216A2.238 2.238 0 0 1 5 13c0-1.01.377-2.042 1.09-2.904.243-.294.526-.569.846-.816M4.92 10A5.493 5.493 0 0 0 4 13H1c0-.26.164-1.03.76-1.724.545-.636 1.492-1.256 3.16-1.275ZM1.5 5.5a3 3 0 1 1 6 0 3 3 0 0 1-6 0m3-2a2 2 0 1 0 0 4 2 2 0 0 0 0-4" />
                                </svg></a>
                        </div>
                        <div class="deposit-name">
                            <p>Invite friends</p>
                        </div>
                    </div>
                    <!-- <div class="deposit d-flex flex-column align-items-center col-3">
                    <div class="icons3">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-gift" viewBox="0 0 16 16">
                            <path d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A2.968 2.968 0 0 1 3 2.506V2.5zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43a.522.522 0 0 0 .023.07zM9 3h2.932a.56.56 0 0 0 .023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0V3zM1 4v2h6V4H1zm8 0v2h6V4H9zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5V7zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5H7z"/>
                          </svg>
                    </div>
                    <div class="deposit-name">
                        <p>Event</p>
                    </div>
                </div> -->
                </div>
            </div>
            <div class="deposit-row container-fluid my-0 " style="margin-bottom:50px !important;">
                <div class="d-flex justify-content-between  row">

                    <div class="deposit d-flex flex-column align-items-center col-4">
                        <a href="about.php">
                            <div class="icons4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-info-circle" viewBox="0 0 16 16">
                                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
                                    <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z" />
                                </svg>
                            </div>
                        </a>
                        <div class="deposit-name">
                            <p>About us</p>
                        </div>
                    </div>

                    <div class="deposit d-flex flex-column align-items-center col-4">
                        <a href="rule.php">
                            <div class="icons5">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-chat" viewBox="0 0 16 16">
                                    <path d="M2.678 11.894a1 1 0 0 1 .287.801 10.97 10.97 0 0 1-.398 2c1.395-.323 2.247-.697 2.634-.893a1 1 0 0 1 .71-.074A8.06 8.06 0 0 0 8 14c3.996 0 7-2.807 7-6 0-3.192-3.004-6-7-6S1 4.808 1 8c0 1.468.617 2.83 1.678 3.894zm-.493 3.905a21.682 21.682 0 0 1-.713.129c-.2.032-.352-.176-.273-.362a9.68 9.68 0 0 0 .244-.637l.003-.01c.248-.72.45-1.548.524-2.319C.743 11.37 0 9.76 0 8c0-3.866 3.582-7 8-7s8 3.134 8 7-3.582 7-8 7a9.06 9.06 0 0 1-2.347-.306c-.52.263-1.639.742-3.468 1.105z" />
                                </svg>
                            </div>
                        </a>
                        <div class="deposit-name">
                            <p>Rule description</p>
                        </div>
                    </div>

                    <!-- <div class="deposit d-flex flex-column align-items-center col-3">
                        <div class="icons6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-hand-thumbs-up" viewBox="0 0 16 16">
                                <path d="M8.864.046C7.908-.193 7.02.53 6.956 1.466c-.072 1.051-.23 2.016-.428 2.59-.125.36-.479 1.013-1.04 1.639-.557.623-1.282 1.178-2.131 1.41C2.685 7.288 2 7.87 2 8.72v4.001c0 .845.682 1.464 1.448 1.545 1.07.114 1.564.415 2.068.723l.048.03c.272.165.578.348.97.484.397.136.861.217 1.466.217h3.5c.937 0 1.599-.477 1.934-1.064a1.86 1.86 0 0 0 .254-.912c0-.152-.023-.312-.077-.464.201-.263.38-.578.488-.901.11-.33.172-.762.004-1.149.069-.13.12-.269.159-.403.077-.27.113-.568.113-.857 0-.288-.036-.585-.113-.856a2.144 2.144 0 0 0-.138-.362 1.9 1.9 0 0 0 .234-1.734c-.206-.592-.682-1.1-1.2-1.272-.847-.282-1.803-.276-2.516-.211a9.84 9.84 0 0 0-.443.05 9.365 9.365 0 0 0-.062-4.509A1.38 1.38 0 0 0 9.125.111L8.864.046zM11.5 14.721H8c-.51 0-.863-.069-1.14-.164-.281-.097-.506-.228-.776-.393l-.04-.024c-.555-.339-1.198-.731-2.49-.868-.333-.036-.554-.29-.554-.55V8.72c0-.254.226-.543.62-.65 1.095-.3 1.977-.996 2.614-1.708.635-.71 1.064-1.475 1.238-1.978.243-.7.407-1.768.482-2.85.025-.362.36-.594.667-.518l.262.066c.16.04.258.143.288.255a8.34 8.34 0 0 1-.145 4.725.5.5 0 0 0 .595.644l.003-.001.014-.003.058-.014a8.908 8.908 0 0 1 1.036-.157c.663-.06 1.457-.054 ************.***********.65.107.308.087.67-.266 1.022l-.353.353.353.354c.***************.154.315.**************.075.581 0 .212-.027.414-.075.582-.05.174-.111.272-.154.315l-.353.353.353.354c.***************.005.488a2.224 2.224 0 0 1-.505.805l-.353.353.353.354c.**************.041.17a.866.866 0 0 1-.121.416c-.165.288-.503.56-1.066.56z"/>
                            </svg>
                        </div>
                        <div class="deposit-name">
                            <p>Promotion description</p>
                        </div>
                    </div> -->

                    <!-- <div class="deposit d-flex flex-column align-items-center col-3">
                        <a href="plan.php">
                            <div class="icons7">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-star" viewBox="0 0 16 16">
                                    <path d="M2.866 14.85c-.078.444.36.791.746.593l4.39-2.256 4.389 2.256c.386.198.824-.149.746-.592l-.83-4.73 3.522-3.356c.33-.314.16-.888-.282-.95l-4.898-.696L8.465.792a.513.513 0 0 0-.927 0L5.354 5.12l-4.898.696c-.441.062-.612.636-.283.95l3.523 3.356-.83 4.73zm4.905-2.767-3.686 1.894.694-3.957a.565.565 0 0 0-.163-.505L1.71 6.745l4.052-.576a.525.525 0 0 0 .393-.288L8 2.223l1.847 3.658a.525.525 0 0 0 .393.288l4.052.575-2.906 2.77a.565.565 0 0 0-.163.506l.694 3.957-3.686-1.894a.503.503 0 0 0-.461 0z" />
                                </svg>
                            </div>
                        </a>
                        <div class="deposit-name">
                            <p>VIP
                            </p>
                        </div>
                    </div> -->

                    <div class="deposit d-flex flex-column align-items-center col-4">
                        <!-- <a href="wallet_history.php"> -->
                        <a href="wallet_address.php">
                            <div class="icons7">
                                <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#fff" class="bi bi-wallet2" viewBox="0 0 16 16">
                                    <path d="M12.136.326A1.5 1.5 0 0 1 14 1.78V3h.5A1.5 1.5 0 0 1 16 4.5v9a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 0 13.5v-9a1.5 1.5 0 0 1 1.432-1.499L12.136.326zM5.562 3H13V1.78a.5.5 0 0 0-.621-.484L5.562 3zM1.5 4a.5.5 0 0 0-.5.5v9a.5.5 0 0 0 .5.5h13a.5.5 0 0 0 .5-.5v-9a.5.5 0 0 0-.5-.5h-13z"></path>
                                </svg>
                            </div>
                        </a>
                        <div class="deposit-name">
                            <p>Wallet Address</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- <div class="deposit-row container-fluid my-0">
                <div class="d-flex justify-content-center row">
                    <div class="deposit d-flex flex-column align-items-center col-4">
                        <a href="support.php">
                            <div class="icons">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-info-circle" viewBox="0 0 16 16">
                                    <path d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5"></path>
                                </svg>
                            </div>
                        </a>
                        <div class="deposit-name">
                            <p>Feedback</p>
                        </div>
                    </div>
                </div>
            </div> -->

        </div>




        <!-- TradingView Widget BEGIN -->
        <!-- <div class="tradingview-widget-container">
                <div class="tradingview-widget-container__widget"></div>
                <div class="tradingview-widget-copyright"><a href="https://www.tradingview.com/" rel="noopener nofollow" target="_blank"><span class="blue-text">Track all markets on TradingView</span></a></div>
                <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-screener.js" async>
                {
                "width": "100%",
                "height": "100%",
                "defaultColumn": "overview",
                "screener_type": "crypto_mkt",
                "displayCurrency": "USD",
                "colorTheme": "light",
                "locale": "en",
                "isTransparent": false
            }
                </script>
            </div> -->
        <!-- TradingView Widget END -->


        <!-- <div class="logo1">
                <img src="assets/image/9130340-mlm-logo-mlm-letter-mlm-letter-logo-design-initials-mlm-logo-linked-with-circle-and-uppercase-monogram-logo-mlm-typography-for-technology-business-and-real-marque-immobilier-vectoriel.jpg" alt="">
            </div>
            <div class="client-name d-flex justify-content-between w-100 flex-column">
                <div class="name">
                    <p class="mb-0">Exclusive member</p>
                </div>
                <div class="refresh">
                   <p>Enjoy the privileged line and get exclusive rewards</p>
                </div>
            </div>
        </div>
        <div class="logo d-flex align-items-center">
            <div class="logo1">
                <img src="assets/image/9130340-mlm-logo-mlm-letter-mlm-letter-logo-design-initials-mlm-logo-linked-with-circle-and-uppercase-monogram-logo-mlm-typography-for-technology-business-and-real-marque-immobilier-vectoriel.jpg" alt="">
            </div>
            <div class="client-name d-flex justify-content-between w-100 flex-column">
                <div class="name">
                    <p class="mb-0">AUTO</p>
                </div>
                <div class="refresh">
                   <p>Intelligent operation, stable income</p>
                </div>
            </div>
        </div>
        <div class="logo d-flex align-items-center">
            <div class="logo1">
                <img src="assets/image/9130340-mlm-logo-mlm-letter-mlm-letter-logo-design-initials-mlm-logo-linked-with-circle-and-uppercase-monogram-logo-mlm-typography-for-technology-business-and-real-marque-immobilier-vectoriel.jpg" alt="">
            </div>
            <div class="client-name d-flex justify-content-between w-100 flex-column">
                <div class="name">
                    <p class="mb-0">Wealth management function</p>
                </div>
                <div class="refresh">
                   <p>Long - term investment, stable income</p>
                </div>
            </div>
        </div> -->
        <div id="popup1" class="popup-container">
            <div class="popup-content">
                <a href="#" class="close">&times;</a>
                <h3>Invite Your Friends</h3>
                <p>Share the link below with your friends:</p>
                <div class="input-group mb-3">
                    <input type="text" class="form-control" id="invite-link" value="<?php echo $base_link; ?>register.php?ref=<?php echo $user['sponsor_id']; ?>" readonly>
                    <button class="btn btn-primary" onclick="copyLink()">Copy Link</button>
                </div>
            </div>
        </div>



        <div class="footer bg-white p-2 d-flex align-items-center justify-content-around">
            <a href="index.php">
                <div class="dashboard d-flex flex-column align-items-center">

                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="#823fb5" class="bi bi-house" viewBox="0 0 16 16">
                        <path d="M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.146a.5.5 0 0 0 .708.708L2 8.207V13.5A1.5 1.5 0 0 0 3.5 15h9a1.5 1.5 0 0 0 1.5-1.5V8.207l.646.647a.5.5 0 0 0 .708-.708L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293zM13 7.207V13.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V7.207l5-5z" />
                    </svg>
                    <p class="m-0 fontweight500 colorgreen fontsize18">Home</p>
                </div>
            </a>

            <a href="wallet_history.php">
                <div class="dashboard d-flex flex-column align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="grey" class="bi bi-file-earmark-text" viewBox="0 0 16 16">
                        <path d="M5.5 7a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5zM5 9.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5z"></path>
                        <path d="M9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.5L9.5 0zm0 1v2A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"></path>
                    </svg>
                    <p class="m-0 fontweight500 fontsize18 colorgrey">History</p>
                </div>
            </a>

            <!-- <a href="leads.php">
                <div class="dashboard1 d-flex flex-column align-items-center"></div>
            </a> -->

            <a href="service.php">
                <div class="dashboard d-flex flex-column align-items-center">

                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="grey" class="bi bi-headset" viewBox="0 0 16 16">
                        <path d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5" />
                    </svg>
                    <p class="m-0 fontweight500 fontsize18 colorgrey">Service</p>
                </div>
                <a href="profile.php">
                    <div class="dashboard d-flex flex-column align-items-center">

                        <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="grey" class="bi bi-person" viewBox="0 0 16 16">
                            <path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6Zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4Zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10Z" />
                        </svg>
                        <p class="m-0 fontweight500 fontsize18 colorgrey">Profile</p>
                    </div>
                </a>

        </div>





        <div id="overlay" class="fixed z3 top-0 left-0 bg-darken-4"></div>
        <div id="sidenav" class="fixed z4 top-0 left-0 bg-white ">
            <div class="id d-flex align-items-center p2">
                <div class="image d-flex align-items-center justify-content-center mr25">
                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-person-fill" viewBox="0 0 16 16">
                        <path d="M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H3Zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" />
                    </svg>
                </div>
                <div class="imagename d-flex flex-column">
                    <p class="m-0 fontweight500 fontsize14">Rohan</p>
                    <p class="m-0 fontweight500 fontsize13">My Total Assets</p>
                    <p class="m-0 fontweight500 fontsize13">
                        <?php if ($res_wallet_amount['ttlamount'] == "") {
                            echo '0.00';
                        } else {
                            echo $res_wallet_amount['ttlamount'];
                        } ?>

                    </p>
                </div>
            </div>

            <ul class="list-reset muted m0 mt25">
                <li class="pointer mb2  ml-1 d-flex align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#009688" class="bi bi-grid-fill  mr25" viewBox="0 0 16 16">
                        <path d="M1 2.5A1.5 1.5 0 0 1 2.5 1h3A1.5 1.5 0 0 1 7 2.5v3A1.5 1.5 0 0 1 5.5 7h-3A1.5 1.5 0 0 1 1 5.5v-3zm8 0A1.5 1.5 0 0 1 10.5 1h3A1.5 1.5 0 0 1 15 2.5v3A1.5 1.5 0 0 1 13.5 7h-3A1.5 1.5 0 0 1 9 5.5v-3zm-8 8A1.5 1.5 0 0 1 2.5 9h3A1.5 1.5 0 0 1 7 10.5v3A1.5 1.5 0 0 1 5.5 15h-3A1.5 1.5 0 0 1 1 13.5v-3zm8 0A1.5 1.5 0 0 1 10.5 9h3a1.5 1.5 0 0 1 1.5 1.5v3a1.5 1.5 0 0 1-1.5 1.5h-3A1.5 1.5 0 0 1 9 13.5v-3z"></path>
                    </svg>
                    <p class="m-0 fontweight500 fontsize15">Dashboard</p>
                </li>
                <li class="pointer mb2 ml-1 d-flex  align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#F44336" class="bi bi-download mr25" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z" />
                        <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z" />
                    </svg>
                    <p class="m-0 fontweight500 fontsize15">Deposits</p>
                </li>
            </ul>
            <hr>
            <ul class="list-reset muted m0 ">

                <li class="pointer mb2  ml-1 d-flex align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#FF9800" class="bi bi-gift mr25" viewBox="0 0 16 16">
                        <path d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A2.968 2.968 0 0 1 3 2.506V2.5zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43a.522.522 0 0 0 .023.07zM9 3h2.932a.56.56 0 0 0 .023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0V3zM1 4v2h6V4H1zm8 0v2h6V4H9zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5V7zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5H7z"></path>
                    </svg>
                    <p class="m-0 fontweight500 fontsize15">Claim</p>
                </li>

                <li class="pointer mb2 ml-1 d-flex align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#009688" class="bi bi-arrow-up-right mr25" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M14 2.5a.5.5 0 0 0-.5-.5h-6a.5.5 0 0 0 0 1h4.793L2.146 13.146a.5.5 0 0 0 .708.708L13 3.707V8.5a.5.5 0 0 0 1 0v-6z"></path>
                    </svg>
                    <p class="m-0 fontweight500 fontsize15">Withdrawal</p>
                </li>
            </ul>
            <hr>
            <ul class="list-reset muted m0 ">

                <li class="pointer mb2  ml-1 d-flex align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#E91E63" class="bi bi-wallet2 mr25" viewBox="0 0 16 16">
                        <path d="M12.136.326A1.5 1.5 0 0 1 14 1.78V3h.5A1.5 1.5 0 0 1 16 4.5v9a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 0 13.5v-9a1.5 1.5 0 0 1 1.432-1.499L12.136.326zM5.562 3H13V1.78a.5.5 0 0 0-.621-.484L5.562 3zM1.5 4a.5.5 0 0 0-.5.5v9a.5.5 0 0 0 .5.5h13a.5.5 0 0 0 .5-.5v-9a.5.5 0 0 0-.5-.5h-13z"></path>
                    </svg>
                    <p class="m-0 fontweight500 fontsize15">Wallet</p>
                </li>

                <li class="pointer mb2 ml-1 d-flex align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#E91E63" class="bi bi-people mr25" viewBox="0 0 16 16">
                        <path d="M15 14s1 0 1-1-1-4-5-4-5 3-5 4 1 1 1 1h8Zm-7.978-1A.261.261 0 0 1 7 12.996c.001-.264.167-1.03.76-1.72C8.312 10.629 9.282 10 11 10c1.717 0 2.687.63 3.24 1.276.593.69.758 1.457.76 1.72l-.008.002a.274.274 0 0 1-.014.002H7.022ZM11 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm3-2a3 3 0 1 1-6 0 3 3 0 0 1 6 0ZM6.936 9.28a5.88 5.88 0 0 0-1.23-.247A7.35 7.35 0 0 0 5 9c-4 0-5 3-5 4 0 .667.333 1 1 1h4.216A2.238 2.238 0 0 1 5 13c0-1.01.377-2.042 1.09-2.904.243-.294.526-.569.846-.816ZM4.92 10A5.493 5.493 0 0 0 4 13H1c0-.26.164-1.03.76-1.724.545-.636 1.492-1.256 3.16-1.275ZM1.5 5.5a3 3 0 1 1 6 0 3 3 0 0 1-6 0Zm3-2a2 2 0 1 0 0 4 2 2 0 0 0 0-4Z" />
                    </svg>
                    <p class="m-0 fontweight500 fontsize15">Team</p>
                </li>
                <li class="pointer mb2 ml-1 d-flex align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#F44336" class="bi bi-info-circle mr25" viewBox="0 0 16 16">
                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
                        <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z" />
                    </svg>
                    <p class="m-0 fontweight500 fontsize15">About Us</p>
                </li>
            </ul>
            <hr>
            <ul class="list-reset muted m0 ">

                <li class="pointer mb2  ml-1 d-flex align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#FF9800" class="bi bi-translate mr25" viewBox="0 0 16 16">
                        <path d="M4.545 6.714 4.11 8H3l1.862-5h1.284L8 8H6.833l-.435-1.286H4.545zm1.634-.736L5.5 3.956h-.049l-.679 2.022H6.18z" />
                        <path d="M0 2a2 2 0 0 1 2-2h7a2 2 0 0 1 2 2v3h3a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-3H2a2 2 0 0 1-2-2V2zm2-1a1 1 0 0 0-1 1v7a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H2zm7.138 9.995c.193.301.402.583.63.846-.748.575-1.673 1.001-2.768 1.292.178.217.451.635.555.867 1.125-.359 2.08-.844 2.886-1.494.777.665 1.739 1.165 2.93 1.472.133-.254.414-.673.629-.89-1.125-.253-2.057-.694-2.82-1.284.681-.747 1.222-1.651 1.621-2.757H14V8h-3v1.047h.765c-.318.844-.74 1.546-1.272 2.13a6.066 6.066 0 0 1-.415-.492 1.988 1.988 0 0 1-.94.31z" />
                    </svg>
                    <p class="m-0 fontweight500 fontsize15">Language</p>
                </li>

                <li class="pointer mb2 ml-1 d-flex align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#009688" class="bi bi-headset mr25" viewBox="0 0 16 16">
                        <path d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5z" />
                    </svg>
                    <p class="m-0 fontweight500 fontsize15">Customer Service</p>
                </li>
            </ul>
            <hr>
            <ul class="list-reset muted m0 ">

                <li class="pointer mb2  ml-1 d-flex align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#E91E63" class="bi bi-box-arrow-right mr25" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M10 12.5a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v2a.5.5 0 0 0 1 0v-2A1.5 1.5 0 0 0 9.5 2h-8A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0v2z" />
                        <path fill-rule="evenodd" d="M15.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L14.293 7.5H5.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z" />
                    </svg>
                    <p class="m-0 fontweight500 fontsize15">Log Out</p>
                </li>


            </ul>
            <hr>


        </div>


        <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="exampleModalLabel">Select Language</h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="google_element">

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary">Save changes</button>
                    </div>
                </div>
            </div>
        </div>
        <style>
            .goog-te-gadget .goog-te-combo {
                margin: 4px 0;
                width: 100% !important;
                font-size: 24px;
            }

            .goog-te-gadget span {
                display: none;
            }

            .goog-te-gadget {
                font-family: arial;
                font-size: 11px;
                color: #fff;
                white-space: nowrap;
            }

            .VIpgJd-ZVi9od-ORHb-OEVmcd {
                left: 0;
                top: 0;
                height: 39px;
                width: 100%;
                z-index: 10000001;
                position: fixed;
                border: none;
                border-bottom: 1px solid #6B90DA;
                margin: 0;
                box-shadow: 0 0 8px 1px #999;
                display: none;
            }

            body {
                top: 0px !important;
            }
        </style>
        <script>
            function copyLink() {
                /* Get the text field */
                var copyText = document.getElementById("invite-link");

                /* Select the text field */
                copyText.select();
                copyText.setSelectionRange(0, 99999); /* For mobile devices */

                /* Copy the text inside the text field */
                document.execCommand("copy");

                /* Alert the copied text */
                //   alert("Copied the link: " + copyText.value);
            }
        </script>

        <script src="assets/nav.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>


        <script>
            let currentSlide = 0;
            const slides = document.querySelectorAll('.slide');
            const totalSlides = slides.length;
            let slideInterval;

            const showSlide = (n) => {
                slides[currentSlide].classList.remove('active');
                currentSlide = (n + totalSlides) % totalSlides;
                slides[currentSlide].classList.add('active');
            }

            const nextSlide = () => showSlide(currentSlide + 1);

            const prevSlide = () => showSlide(currentSlide - 1);

            const startSlideShow = () => slideInterval = setInterval(nextSlide, 5000); // Change slide every 5 seconds

            const stopSlideShow = () => clearInterval(slideInterval);

            document.getElementById('prevBtn').addEventListener('click', prevSlide);
            document.getElementById('nextBtn').addEventListener('click', nextSlide);

            // Pause the slideshow when the mouse is over the slider
            document.querySelector('.slider-container').addEventListener('mouseenter', stopSlideShow);

            // Restart the slide show when the mouse leaves the slider
            document.querySelector('.slider-container').addEventListener('mouseleave', startSlideShow);

            // Show the first slide and start the slide show when the page loads
            showSlide(currentSlide);
            startSlideShow();
        </script>
        <style>
            /* Slider */

            .slider-container {
                position: relative;
                overflow: hidden;
                width: 100%;
                /* max-width: 100vw; */
                max-width: 364px;
                min-height: 550px;
                margin: auto;
            }

            .slide {
                display: flex;
                flex: 0 0 100%;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                transition: transform 1s ease-in-out;
                transform: translateX(100%);
            }

            .slide.active {
                transform: translateX(0);
            }

            .text-box {
                flex: 3;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;
                color: #fff;
                padding: 4em;
                box-sizing: border-box;
                width: 30%;
            }

            .imge img {
                object-fit: cover;
                flex: 7;
                width: 70px;
                height: auto;
                box-sizing: border-box;
            }

            .arrow {
                position: absolute;
                top: 50%;
                font-size: 24px;
                color: #fff;
                background: none;
                border: none;
                cursor: pointer;
                outline: none;
            }

            #prevBtn {
                left: 10px;
            }

            #nextBtn {
                right: 10px;
            }

            /* Background colors for each slide*/

            .news_img {
                background-size: cover;
                background-position: center;
                padding-top: 70%;
                width: 100%;
            }

            .imge video {
                width: 100%;
                margin-top: 10px;
                margin-bottom: 10px;
            }


            /* Tablet */
            @media screen and (max-width: 768px) {
                .slide {
                    flex-direction: column;
                }

                .text-box {
                    order: 2;
                    width: 100%;
                    padding: 2em;
                }

                .imge img {
                    width: 100%;
                }
            }

            /* Mobile */
            @media screen and (max-width: 480px) {
                .slide {
                    flex-direction: column;
                }

                .text-box {
                    order: 2;
                    width: 100%;
                    padding: 1em;
                }

                .imge img {
                    width: 100%;
                }
            }
        </style>
</body>

</html>