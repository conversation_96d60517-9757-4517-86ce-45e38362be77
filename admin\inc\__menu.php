<div class="topbar-nav header navbar" role="banner">
    <nav id="topbar">
        <ul class="navbar-nav theme-brand flex-row  text-center">
            <li class="nav-item theme-text">
                <a href="home.php" class="nav-link">
                    <?php echo SITE; ?>
                </a>
            </li>
        </ul>

        <ul class="list-unstyled menu-categories" id="topAccordion">

            <li class="menu single-menu <?php if ($page == 1) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="home.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-home">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9 22 9 12 15 12 15 22"></polyline>
                        </svg>
                        <span>Home</span>
                    </div>

                </a>
            </li>

            <li class="menu single-menu <?php if ($page == 3) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="#app" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-user">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                        <span>Users</span>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-chevron-down">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </a>
                <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                    <li class="<?php if ($sub_page == 31) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="users.php"> All Users </a>
                    </li>

                    <li class="<?php if ($sub_page == 32) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="active_users.php"> Active Users </a>
                    </li>

                    <li class="<?php if ($sub_page == 33) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="deactive_users.php"> De-Active Users </a>
                    </li>

                </ul>
            </li>


            <!-- <li class="menu single-menu <?= ($page == 13) ? 'active' : ''; ?>">
                <a href="plan_requested.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-plus-square">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="12" y1="8" x2="12" y2="16"></line>
                            <line x1="8" y1="12" x2="16" y2="12"></line>
                        </svg>
                        <span>Plan Request</span>
                    </div>
                </a>
            </li> -->


            <li class="menu single-menu <?= ($page == 13) ? 'active' : ''; ?>">
                <a href="#app13" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-plus-square">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="12" y1="8" x2="12" y2="16"></line>
                            <line x1="8" y1="12" x2="16" y2="12"></line>
                        </svg>

                        <!-- <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-user">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                        </svg> -->
                        <span>Plans</span>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-chevron-down">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </a>
                <ul class="collapse submenu list-unstyled" id="app13" data-parent="#topAccordion">
                    <li class="<?= ($sub_page == 131) ? 'active' : ''; ?>">
                        <a href="plans.php"> Plans </a>
                    </li>

                    <li class="<?= ($sub_page == 132) ? 'active' : ''; ?>">
                        <a href="referral_income.php">Referral Income </a>
                    </li>
                    <li class="<?= ($sub_page == 133) ? 'active' : ''; ?>">
                        <a href="level_income.php">Level Income </a>
                    </li>
                    <li class="<?= ($sub_page == 134) ? 'active' : ''; ?>">
                        <a href="leadership_income.php">Leadership Income </a>
                    </li>
                    <li class="<?= ($sub_page == 135) ? 'active' : ''; ?>">
                        <a href="direct_joining_income.php">Direct Joining Income </a>
                    </li>

                    <li class="<?= ($sub_page == 136) ? 'active' : ''; ?>">
                        <a href="plan_requested.php"> Plan Request </a>
                    </li>

                    <!-- <li class="<?= ($sub_page == 133) ? 'active' : ''; ?>">
                        <a href="deactive_users.php"> De-Active Users </a>
                    </li> -->

                </ul>
            </li>





            <li class="menu single-menu <?php if ($page == 15) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="user_wallet.php">
                    <div class="">
                        <!-- <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg> -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#fff" class="bi bi-wallet2"
                            viewBox="0 0 16 16">
                            <path
                                d="M12.136.326A1.5 1.5 0 0 1 14 1.78V3h.5A1.5 1.5 0 0 1 16 4.5v9a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 0 13.5v-9a1.5 1.5 0 0 1 1.432-1.499L12.136.326zM5.562 3H13V1.78a.5.5 0 0 0-.621-.484L5.562 3zM1.5 4a.5.5 0 0 0-.5.5v9a.5.5 0 0 0 .5.5h13a.5.5 0 0 0 .5-.5v-9a.5.5 0 0 0-.5-.5h-13z">
                            </path>
                        </svg>
                        <span>User Wallet</span>
                    </div>
                </a>
            </li>


            <style>
                .active a div .bi-wallet2 {
                    color: #25d5e4;
                    fill: #25d5e4 !important;
                }
            </style>

            <li class="menu single-menu <?php if ($page == 11) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="withdrawl.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-arrow-down-circle">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="8 12 12 16 16 12"></polyline>
                            <line x1="12" y1="8" x2="12" y2="16"></line>
                        </svg>
                        <span>Withdraw Request</span>
                    </div>

                </a>
            </li>


            <!--                                 
                    <li class="menu single-menu <?php if ($page == 6) {
                                                    echo 'active';
                                                } else {
                                                    echo '';
                                                } ?>">
                        <a href="#app" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                            <div class="">
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-sidebar"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="9" y1="3" x2="9" y2="21"></line></svg>
                                <span>Wallet</span>
                            </div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down"><polyline points="6 9 12 15 18 9"></polyline></svg>
                        </a>
                        <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                            <li class="<?php if ($sub_page == 61) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="fund_transfer_user_wallet.php"> Fund Transfer To User Wallet </a>
                            </li>
                            
                             <li class="<?php if ($sub_page == 68) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="fund_transfer_bonanaza_wallet.php"> Fund Transfer To Bonanaza<br/> Wallet </a>
                            </li>
                            
                            <li class="<?php if ($sub_page == 62) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="admin_wallet.php"> Admin Wallet </a>
                            </li>
                            
                            <li class="<?php if ($sub_page == 67) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="main_wallet.php"> Main Wallet </a>
                            </li>
                            
                            <li class="<?php if ($sub_page == 66) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="referral_wallet.php"> Referral Wallet </a>
                            </li>
                            
                            <li class="<?php if ($sub_page == 63) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="autopool_wallet.php"> Autopool Wallet </a>
                            </li>
                            
                            <li class="<?php if ($sub_page == 64) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="reward_wallet.php"> Reward Wallet </a>
                            </li>
                            
                            <li class="<?php if ($sub_page == 65) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="salary_wallet.php"> Salary Wallet </a>
                            </li>
                             
                             
                       </ul>
                    </li>
                    
                    <li class="menu single-menu <?php if ($page == 10) {
                                                    echo 'active';
                                                } else {
                                                    echo '';
                                                } ?>">
                        <a href="deposite.php">
                            <div class="">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>
                                <span>Deposite Request</span>
                            </div>

                        </a>                        
                    </li>                     
                    
                                                        
                    
                    <li class="menu single-menu <?php if ($page == 12) {
                                                    echo 'active';
                                                } else {
                                                    echo '';
                                                } ?>">
                        <a href="#app" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                            <div class="">
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-alert-circle"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>
                                <span>Notice</span>
                            </div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down"><polyline points="6 9 12 15 18 9"></polyline></svg>
                        </a>
                        <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                            <li class="<?php if ($sub_page == 121) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="notice.php"> Notice </a>
                            </li>
                            
                            <li class="<?php if ($sub_page == 122) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="login_notes.php"> Login Notes</a>
                            </li>
                            
                            
                             
                       </ul>
                    </li>
                    -->

            <li class="menu single-menu <?php if ($page == 14) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="news.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-tv">
                            <rect x="2" y="7" width="20" height="15" rx="2" ry="2"></rect>
                            <polyline points="17 2 12 7 7 2"></polyline>
                        </svg>
                        <span>News Room</span>
                    </div>
                </a>
            </li>

            <li class="menu single-menu <?php if ($page == 4) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="change_password.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-lock">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                        </svg>
                        <span>Change Password</span>
                    </div>

                </a>
            </li>


            <li class="menu single-menu <?php if ($page == 5) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="contactus.php">
                    <div class="">
                        <!-- <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-lock">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                        </svg> -->

                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                            class="bi bi-headphone" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 16 16">
                            <path
                                d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5">
                            </path>
                        </svg>

                        <span>Feedback</span>
                    </div>

                </a>
            </li>

            <style>
                .active a div .bi-headphone {
                    color: #25d5e4;
                    fill: #25d5e4 !important;
                }
            </style>


            <li class="menu single-menu <?php if ($page == 12) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="notification.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-alert-circle">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="12"></line>
                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                        </svg>
                        <span>Notification</span>
                    </div>
                </a>
            </li>
        </ul>
    </nav>
</div>