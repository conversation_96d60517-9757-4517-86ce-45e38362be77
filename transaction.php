<?php
session_start();
// include('./admin/lib/db_connection.php');
include('./lib/auth.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');

$sel = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
$res = dbFetchAssoc($sel);
$requested_plan = dbQuery("SELECT * FROM tabl_plan_transaction WHERE user_id='" . $_SESSION['user_id'] . "' AND (`status`='1' OR `status`='0')");
$r_req_plan = dbNumRows($requested_plan);
if ($r_req_plan > 0) {
    echo '<script>window.location.href="plan.php";</script>';
}

$plan_id = $_REQUEST['plan_id'];
$amount = $_REQUEST['amount'];

$sel = dbQuery("SELECT * FROM tabl_plans WHERE id='" . $plan_id . "'");
if ($res = dbFetchAssoc($sel)) {
    if ($amount >= $res['min_price'] && $amount <= $res['max_price']) {
        // Amount is valid, proceed with the next steps
    } else {
        echo "<script>alert('Amount should be greater than " . $res['min_price'] . " and less than " . $res['max_price'] . "!');window.location.href = 'plan_amount.php?plan_id=$plan_id';</script>";
        die();
    }
} else {
    echo "<script>alert('Invalid Plan ID!');window.location.href = 'plan_amount.php?plan_id=$plan_id';</script>";
    die();
}


if (isset($_REQUEST['submit'])) {

    if ($_FILES["screen_shot"]["name"] != "") {
        $target_dir = "./assets/image/screen_shot/";
        $name = rand(10000, 1000000);
        $extension = pathinfo($_FILES["screen_shot"]["name"], PATHINFO_EXTENSION);
        $new_name = $name . "." . $extension;
        $target_file = $target_dir . $name . "." . $extension;

        $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
        if ($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg") {
            die("This is not valid image. Please try again.");
        } else {
            move_uploaded_file($_FILES["screen_shot"]["tmp_name"], $target_file);
            $screen_shot = $new_name;

            // $sel = dbQuery("SELECT * FROM tabl_plans WHERE id='" . $plan_id . "'");
            // $res = dbFetchAssoc($sel);
            // calculating total price with tax
            // $totalPrice = (int)$res['tax_amt'] + (int)$res['price'];

            $totalPrice = $amount;

            $result = dbQuery("INSERT INTO tabl_plan_transaction SET user_id='" . $_SESSION['user_id'] . "',plan_id='" . $plan_id . "',price='" . $totalPrice . "',reference_id='" . mysqli_real_escape_string($con, $_REQUEST['reference_id']) . "',screen_shot='" . $screen_shot . "',date_added='" . $date . "'");
            if ($result) {
                // echo 1;
                echo "<script>alert('Plan Purchase Request addded! We will authorize the transaction and apply the plan as you requested!');window.location.href = 'index.php';</script>";
            } else {
                // echo 0;
                echo "<script>alert('Plan Purchase Request failed!');window.location.href = 'index.php';</script>";
            }
        }
    } else {
        // $image_name = '';
        echo "<script>alert('Plan Purchase Request failed!');window.location.href = 'index.php';</script>";
    }

    die();
}


// $plan_id = $_REQUEST['plan_id'];
// $amount = $_REQUEST['amount'];

// $sel = dbQuery("SELECT * FROM tabl_plans WHERE id='" . $_REQUEST['plan_id'] . "'");
// if (!$res = dbFetchAssoc($sel)) {
//     echo "<script>alert('Please enter a valid amount!');window.location.href = 'plan_amount.php?plan_id=$plan_id';</script>";
// }



// $sel = dbQuery("SELECT * FROM tabl_plans WHERE id='" . $plan_id . "'");
// if ($res = dbFetchAssoc($sel)) {
//     if ($amount >= $res['min_price'] && $amount <= $res['max_price']) {
//         // Amount is valid, proceed with the next steps
//     } else {
//         echo "<script>alert('Amount should be greater than " . $res['min_price'] . " and less than " . $res['max_price'] . "!');window.location.href = 'plan_amount.php?plan_id=$plan_id';</script>";
//     }
// } else {
//     echo "<script>alert('Invalid Plan ID!');window.location.href = 'plan_amount.php?plan_id=$plan_id';</script>";
// }



?>

<!doctype html>
<html lang="en">

<head>
    <meta cha$et="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Home</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <script src="https://cdn.rawgit.com/davidshimjs/qrcodejs/gh-pages/qrcode.min.js"></script>

    <link rel="stylesheet" href="assets/nav.css">
    <style>
        .banner {
            /* border: 1px solid purple; */
            margin: 10px;
            background: #ecc7f1;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .line {
            padding: -4px;
            width: 100%;
            /* text-align: center; */
            /* justify-content: center; */
            height: 1px;
            background: black;
            margin-top: -4px;
        }

        .banner h6 {
            font-size: 15px;
            font-weight: 600;
        }

        .banner .fee {
            margin-top: -20px;
            font-weight: 500;
        }

        .fee p {
            font-style: italic;
        }

        input {
            border: none;
            border: 0.6 solid #bb3ca1;
            border-bottom: 2px solid #bea2d9;
            border-radius: 20px;
            height: 38px;
        }

        input:hover {
            border: 1 px solid #bea2d9;
        }

        .next {
            text-align: center;
        }

        .button {
            width: 100%;
            text-align: center;
            display: inline-block;
            padding: 10px 20px;
            text-decoration: none;
            background-color: #823fb5;
            color: #fff !important;
            border: none;
            border-radius: 20px;
        }

        .px-3 {
            padding-left: 15px !important;
            padding-right: 15px !important;
        }

        #qrcode {
            padding: 20px !important;
            background-color: #fff;
        }

        #qrcode img {
            margin: auto;
        }

        .text-address {
            text-align: right;
        }

        @media (max-width: 540px) {
            .text-address {
                text-align: left;
            }
        }
    </style>
</head>

<body>
    <div class="fullbody1">
        <div class="header">
            <div class="header-items d-flex align-items-center justify-content-between m-2">
                <div class="web w-10" onclick="window.location.href = 'index.php'">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-arrow-left" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z" />
                    </svg>
                </div>

                <div class="home w-90 d-flex justify-content-center">
                    <h4>Transaction</h4>
                </div>

                <div class="web w-10" onclick="window.location.href = 'index.php'">

                    <!--                    
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-headset" viewBox="0 0 16 16">
                        <path d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5"/>
                      </svg> -->
                </div>

            </div>
        </div>
        <div class="banner">
            <div class="row p-2">

                <div class="col">
                    <h6>Deposite Address(BEP20):</h6>
                </div>
                <div class="col ">
                    <p class="text-address"><?php echo TRC_ADDRESS; ?></p>
                </div>

            </div>
            <div class="row p-2">
                <div class="col">
                    <div id="qrcode"></div>
                </div>

                <script>
                    // Function to generate QR code
                    function generateQRCode(text) {
                        var qrcode = new QRCode(document.getElementById("qrcode"), {
                            text: text,
                            width: 200,
                            height: 200
                        });
                    }

                    // Example: Generate QR code for a URL
                    var urlToEncode = "<?php echo TRC_ADDRESS; ?>";
                    generateQRCode(urlToEncode);
                </script>

            </div>
            <hr class="line" />
            <!-- <div class="fee row p-2 ">
                <div class="col">
                    <h6>Plan :</h6>
                </div>
                <div class="col ">
                    <p class="text-end" style="color:red;"><?php echo $amount; ?> USDT</p>
                </div>
            </div> -->
            <!-- <div class="fee row p-2 ">
                <div class="col">
                    <h6>Tax :</h6>
                </div>
                <div class="col ">
                    <p class="text-end" style="color:red;"><?php echo number_format($res['tax_amt'], 2); ?> USDT</p>
                </div>
            </div> -->
            <div class="fee row p-2 ">
                <div class="col">
                    <h6>Total Payable Amount :</h6>
                </div>
                <div class="col ">
                    <!-- <p class="text-end" style="color:red;"><?php echo number_format((int)$res['tax_amt'] + (int)$amount, 2); ?> USDT</p> -->
                    <p class="text-end" style="color:red;"><?php echo number_format((int)$amount, 2); ?> USDT</p>
                </div>
            </div>
            <div class="fee row p-2">
                <div class="col">
                    <h6>Date :</h6>
                </div>
                <div class="col ">
                    <p class="text-end"> <?php echo date('d-m-Y', strtotime($date)); ?></p>
                </div>
            </div>

            <div class="fee row p-2">
                <div class="col">
                    <p class="" style="color:red;font-size: 14px">
                        Note: Please pay plan amount to above BEP20 address after that submit the reference id.
                    </p>
                </div>
            </div>
        </div>

        <div class="formdep m-3">
            <!-- <form action="" id="plan_form"> -->
            <form action="" method="post" enctype="multipart/form-data">
                <div class="deposits d-flex flex-column ">
                    <label for="">Transaction No/ ReferenceID:</label>
                    <input type="text" name="reference_id" class="px-3" required>
                </div>
                <div class="deposits d-flex flex-column ">
                    <label for="">Upload Screenshot</label>
                    <input type="file" name="screen_shot" class="px-3" required>
                </div>

                <div class="deposits d-flex flex-column mt-2">
                    <div class="next mt-4">
                        <div class="msg"></div>

                        <input type="hidden" name="amount" value="<?php echo $amount; ?>">
                        <input type="hidden" name="plan_id" value="<?php echo $plan_id; ?>">
                        <button type="submit" id="submit" name="submit" class="button">PAY</button>

                        <!-- <button type="submit" id="submit" class="btn btn1 btn-primary w-100">PAY</button> -->
                    </div>
                </div>
            </form>
        </div>
    </div>


    <script src="assets/nav.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <script>
        $("#plan_form").submit(function(e) {
            $("#submit").attr('disabled', true);
            $("#submit").html('<i class="spinner-border align-self-center" style="width="20" height="20"></i> processing...');
            $.ajax({
                url: 'ajax/plan_transaction.php',
                type: 'post',
                data: $("#plan_form").serialize(),
                success: function(data) {
                    if (data == 1) {
                        $("#submit").attr('disabled', true);
                        $("#submit").html('PAY');
                        $(".msg").html('<span style="color:green">Plan Purchase Request addded! We will authorize the transaction and apply the plan as you requested!</span>');
                        setTimeout(function() {
                            window.location.href = 'index.php';
                        }, 5000);
                    } else {
                        $("#submit").attr('disabled', true);
                        $("#submit").html('PAY');
                        $(".msg").html('<span style="color:red">Plan Purchase Request failed! We will authorize the transaction and apply the plan as you requested!</span>');
                        setTimeout(function() {
                            window.location.href = 'index.php';
                        }, 5000);
                    }
                },
            });
            e.preventDefault();
        });
    </script>

</body>

</html>