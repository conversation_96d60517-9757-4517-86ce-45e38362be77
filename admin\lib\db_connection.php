<?php
define('HOSTNAME', 'localhost');

// this is the db config of the server so do not changhe it
//  define ('USERNAME', 'btctrlwv_db1');
//  define ('PASSWORD', 'Yq1cV{v7VxmQ');
//  define ('DATABASE_NAME', 'btctrlwv_db1');

//  define ('USERNAME', 'kriworld2_mlm');
//  define ('PASSWORD', 'UmJyZnw7rCppzHaqFEjC');
//  define ('DATABASE_NAME', 'kriworld2_mlm');

define('USERNAME', 'root'); // username of localhost is root
define('PASSWORD', ''); // password should blank
define('DATABASE_NAME', 'kri_btctrade'); // enter db name what we have created now
// define('DATABASE_NAME', 'kri_aitradebull_2');



$con = mysqli_connect(HOSTNAME, USERNAME, PASSWORD, DATABASE_NAME);
if (mysqli_connect_errno()) {
	echo "Failed to connect to MySQL: " . mysqli_connect_error();
}


function dbQuery($sql)
{
	global $con;

	$result = mysqli_query($con, $sql);

	return $result;
}

function dbAffectedRows()
{
	global $con;

	return mysqli_affected_rows($con);
}

// function dbFetchArray($result, $resultType = MYSQL_NUM)
// {
// 	return mysqli_fetch_array($result, $resultType);
// }

function dbFetchAssoc($result)
{
	return mysqli_fetch_assoc($result);
}

function dbFetchRow($result)
{
	return mysqli_fetch_row($result);
}

function dbFreeResult($result)
{
	return mysqli_free_result($result);
}

function dbNumRows($result)
{
	return mysqli_num_rows($result);
}

// function dbSelect($con)
// {
// 	return mysqli_select_db($con);
// }

function dbInsertId()
{
	global $con;
	return mysqli_insert_id($con);
}

$setting = dbQuery("SELECT * FROM tabl_setting WHERE id=1");
$res_setting = dbFetchAssoc($setting);


define("SITE", $res_setting['site_name']);
define("EMAIL", $res_setting['site_email']);
define("TRC_ADDRESS", $res_setting['trc_address']);
define("M_WITHRAW", $res_setting['m_withdraw']);
define("W_CHARGES", $res_setting['w_charges']);
define("S_CHARGES", $res_setting['s_charges']);
define("USD_TO_INR", $res_setting['usdt_to_inr_rate']);


define("WITHDRAW_FROM", $res_setting['withdraw_from']);
define("WITHDRAW_TO", $res_setting['withdraw_to']);


$currencySymbols = "₹";
$currencyCode = "INR";

$currencySymbols = "₹";
$currencyCode = "INR";

// $base_link = "https://aitradebull.com/";
// $base_link = "https://kalingakreators.com/demo_mlm/";
$base_link = "https://btctrade.digital/";
