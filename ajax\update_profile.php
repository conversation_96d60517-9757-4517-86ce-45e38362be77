<?php 
session_start();
include('../admin/lib/db_connection.php');
include('../admin/inc/resize-class.php');
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d H:i:s');

if($_FILES["user_image"]["name"]!=""){		  
		  $target_dir = "../assets/image/";
		  $name = rand(10000,1000000);
		  $extension = pathinfo($_FILES["user_image"]["name"], PATHINFO_EXTENSION);
		  $new_name=$name.".".$extension;
          $target_file = $target_dir . $name.".".$extension;

	$imageFileType = strtolower(pathinfo($target_file,PATHINFO_EXTENSION));	
if($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg") {
    die("This is not valid image. Please try again.");
} else{  
	move_uploaded_file($_FILES["user_image"]["tmp_name"], $target_file);
	 	 $target_path="../assets/image/".$new_name;
			$resizeObj = new resize("../assets/image/".$new_name);
			$resizeObj -> resizeImage(100,100, 'exact');
			$resizeObj -> saveImage("../assets/image/thumb-100/".$new_name, 100);
	
        $upd=dbQuery("UPDATE tabl_user SET name='".mysqli_real_escape_string($con,$_REQUEST['name'])."',email='".$_REQUEST['email']."',user_image='".$new_name."' WHERE id='".$_SESSION['user_id']."'");
	    
   }
 }else{
	 $upd=dbQuery("UPDATE tabl_user SET name='".mysqli_real_escape_string($con,$_REQUEST['name'])."',email='".$_REQUEST['email']."' WHERE id='".$_SESSION['user_id']."'");
       }
echo 1;
