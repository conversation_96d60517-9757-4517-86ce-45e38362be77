<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 13;
$sub_page = 132;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');


if (!isset($_REQUEST['id']) || $_REQUEST['id'] == '') {
  echo "<script>alert('Invalid request!'); window.location.href='referral_income.php';</script>";
}

$id = $_REQUEST['id'];

if (isset($_POST['submit'])) {
  $level = $_POST['level'];
  $referral_income = $_POST['referral_income'];
  // $status = $_POST['status'];

  $date_added = date('Y-m-d H:i:s');

  // $sql = "INSERT INTO tabl_referral_income (rank, min_price, max_price, income, date_added) VALUES ('$package_name', '$min_investment', '$max_investment', '$income', '$date_added');";
  // $sql = "INSERT INTO tabl_referral_income (`rank`, `min_price`, `max_price`, `income`, `status`, `date_added`) VALUES ('$package_name', '$min_investment', '$max_investment', '$income', '1', '$date_added')";
  // $sql = "UPDATE tabl_referral_income SET `rank` = '$package_name', `min_price` = '$min_investment', `max_price` = '$max_investment', `income` = '$income' WHERE `id` = '$id'";

  $sql = "UPDATE tabl_referral_income SET `level` = '$level', `referral_income` = '$referral_income' WHERE `id` = '$id'";

  // echo $sql;
  $result = dbQuery($sql);

  if ($result) {
    echo "<script>alert('Referral Income updated successfully!'); window.location.href='referral_income.php';</script>";
  } else {
    echo "<script>alert('Failed to add plan!'); window.location.href='referral_income.php';</script>";
  }
}



$sel_plan = dbQuery("SELECT * FROM tabl_referral_income WHERE id='$id'");
if (!$res_plan = dbFetchAssoc($sel_plan)) {
  echo "<script>alert('Invalid request!'); window.location.href='referral_income.php';</script>";
}


?>

<!DOCTYPE html>

<html lang="en">

<head>

  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
  <title>
    <?php echo SITE; ?>- Edit Referral Income
  </title>

  <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
  <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
  <script src="assets/js/loader.js"></script>

  <!-- BEGIN GLOBAL MANDATORY STYLES -->
  <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
  <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />

  <!-- END GLOBAL MANDATORY STYLES -->
  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
  <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
  <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />

  <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->
  <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
  <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />

</head>



<body class="alt-menu sidebar-noneoverflow">
  <?php include('inc/__header.php'); ?>

  <!--  BEGIN MAIN CONTAINER  -->
  <div class="main-container" id="container">
    <div class="overlay"></div>
    <div class="search-overlay"></div>

    <!--  BEGIN TOPBAR  -->
    <?php include('inc/__menu.php'); ?>

    <!--  END TOPBAR  -->

    <!--  BEGIN CONTENT PART  -->
    <div id="content" class="main-content">

      <div class="layout-px-spacing">
        <nav class="breadcrumb-one" aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="home.php">Home</a></li>
            <li class="breadcrumb-item"><a href="fund_transfer_user_wallet.php">Referral Incomes</a></li>
            <li class="breadcrumb-item active"><a href="javascript:void(0);"> Edit Referral Income</a></li>
          </ol>
        </nav>
        <div class="account-settings-container layout-top-spacing">
          <div class="account-content">
            <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
              <div class="row">
                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                  <form name="account" method="post" id="general-info" class="section general-info" enctype="multipart/form-data">
                    <div class="info">
                      <h6 class="">Edit Referral Income</h6>
                      <div class="row">
                        <div class="col-lg-11 mx-auto">
                          <div class="row">
                            <div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
                              <div class="form">
                                <div class="row">
                                  <div class="col-sm-6">
                                    <div class="form-group">
                                      <label for="level">Level</label>
                                      <input type="text" class="form-control mb-4" id="level" name="level"
                                        placeholder="Level" autocomplete="off" value="<?= $res_plan['level']; ?>" required>
                                    </div>
                                  </div>

                                  <div class="col-sm-6">
                                    <div class="form-group">
                                      <label for="referral_income">Referral Income</label>
                                      <input type="text" class="form-control mb-4" id="referral_income" name="referral_income"
                                        placeholder="Referral Income" value="<?= $res_plan['referral_income']; ?>" required>
                                    </div>
                                  </div>


                                  <!-- <div class="col-sm-6">
                                    <div class="form-group">
                                      <label for="status">Status</label>
                                      <select class="form-control mb-4" id="status" name="status" required>
                                        <option value="1">Enabled</option>
                                        <option value="0">Disabled</option>
                                      </select>
                                    </div>
                                  </div> -->

                                  <div class="col-sm-12">
                                    <div class="form-group">
                                      <button type="submit" name="submit" class="btn btn-secondary mb-4 mr-2"
                                        id="submit">Submit</button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </form>


                </div>
              </div>
            </div>
          </div>
        </div>
        <?php include('inc/__footer.php'); ?>
      </div>
    </div>

    <!--  END CONTENT PART  -->

  </div>

  <!-- END MAIN CONTAINER -->

  <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->

  <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
  <script src="bootstrap/js/popper.min.js"></script>
  <script src="bootstrap/js/bootstrap.min.js"></script>
  <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
  <script src="assets/js/app.js"></script>
  <script>
    $(document).ready(function() {

      App.init();

    });
  </script>
  <script src="assets/js/custom.js"></script>

  <!-- END GLOBAL MANDATORY SCRIPTS -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

  <script src="plugins/apex/apexcharts.min.js"></script>
  <script src="assets/js/dashboard/dash_2.js"></script>

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

  <script src="plugins/dropify/dropify.min.js"></script>
  <script src="plugins/blockui/jquery.blockUI.min.js"></script>

  <!-- <script src="plugins/tagInput/tags-input.js"></script> -->

  <script src="assets/js/users/account-settings.js"></script>
</body>

</html>
<script>
  $(document).ready(function() {

    $("#sponsor_name").keyup(function() {

      $.ajax({
        type: "POST",

        url: "ajax/search_user.php",

        data: {
          'keyword': $(this).val()
        },

        //data:'keyword='+$(this).val(),

        success: function(data) {

          //alert(data);

          $("#suggesstion-box").show();

          $("#suggesstion-box").html(data);

        }

      });

    });

  });



  function click01(val1) {

    var new_val = val1.split(',');

    $("#user_id").val(new_val[0]);

    $("#sponsor_name").val(new_val[1]);

    $("#suggesstion-box").hide();

    $("#submit").attr('disabled', false);

  }
</script>
<style>
  #search-list {
    float: left;
    list-style: none;
    margin: 0;
    padding: 0;
    width: 93%;
    position: absolute;
    font-family: inherit;
    border-right: 1px solid rgba(158, 149, 149, 0.21);
    z-index: 100;
    margin-top: -24px;
  }

  #search-list li {
    padding: 10px;
    background: #463d3d;
    border-bottom: #F0F0F0 1px solid;
    color: white;
    border-radius: 6px;
    cursor: pointer;
  }
</style>

<script>
  function isNumber(evt) {
    var iKeyCode = (evt.which) ? evt.which : evt.keyCode
    if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
      return false;

    return true;
  }

  function isDecimal(evt, obj) {

    var charCode = (evt.which) ? evt.which : event.keyCode
    var value = obj.value;
    var dotcontains = value.indexOf(".") != -1;
    if (dotcontains)
      if (charCode == 46) return false;
    if (charCode == 46) return true;
    if (charCode > 31 && (charCode < 48 || charCode > 57))
      return false;
    return true;
  }
</script>