@font-face {
font-family: "uicons-solid-rounded";
src: url("../webfonts/uicons-solid-rounded.eot#iefix") format("embedded-opentype"),
url("../webfonts/uicons-solid-rounded.woff2") format("woff2"),
url("../webfonts/uicons-solid-rounded.woff") format("woff");
}

    i[class^="fi-sr-"]:before, i[class*=" fi-sr-"]:before, span[class^="fi-sr-"]:before, span[class*="fi-sr-"]:before {
font-family: uicons-solid-rounded !important;
font-style: normal;
font-weight: normal !important;
font-variant: normal;
text-transform: none;
line-height: 1;
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
}

        .fi-sr-add-document:before {
    content: "\f101";
    }
        .fi-sr-add-folder:before {
    content: "\f102";
    }
        .fi-sr-add:before {
    content: "\f103";
    }
        .fi-sr-address-book:before {
    content: "\f104";
    }
        .fi-sr-alarm-clock:before {
    content: "\f105";
    }
        .fi-sr-alarm-plus:before {
    content: "\f106";
    }
        .fi-sr-alarm-snooze:before {
    content: "\f107";
    }
        .fi-sr-align-center:before {
    content: "\f108";
    }
        .fi-sr-align-justify:before {
    content: "\f109";
    }
        .fi-sr-align-left:before {
    content: "\f10a";
    }
        .fi-sr-align-right:before {
    content: "\f10b";
    }
        .fi-sr-ambulance:before {
    content: "\f10c";
    }
        .fi-sr-angle-circle-down:before {
    content: "\f10d";
    }
        .fi-sr-angle-circle-left:before {
    content: "\f10e";
    }
        .fi-sr-angle-circle-right:before {
    content: "\f10f";
    }
        .fi-sr-angle-circle-up:before {
    content: "\f110";
    }
        .fi-sr-angle-double-left:before {
    content: "\f111";
    }
        .fi-sr-angle-double-right:before {
    content: "\f112";
    }
        .fi-sr-angle-double-small-down:before {
    content: "\f113";
    }
        .fi-sr-angle-double-small-left:before {
    content: "\f114";
    }
        .fi-sr-angle-double-small-right:before {
    content: "\f115";
    }
        .fi-sr-angle-double-small-up:before {
    content: "\f116";
    }
        .fi-sr-angle-down:before {
    content: "\f117";
    }
        .fi-sr-angle-left:before {
    content: "\f118";
    }
        .fi-sr-angle-right:before {
    content: "\f119";
    }
        .fi-sr-angle-small-down:before {
    content: "\f11a";
    }
        .fi-sr-angle-small-left:before {
    content: "\f11b";
    }
        .fi-sr-angle-small-right:before {
    content: "\f11c";
    }
        .fi-sr-angle-small-up:before {
    content: "\f11d";
    }
        .fi-sr-angle-square-down:before {
    content: "\f11e";
    }
        .fi-sr-angle-square-left:before {
    content: "\f11f";
    }
        .fi-sr-angle-square-right:before {
    content: "\f120";
    }
        .fi-sr-angle-square-up:before {
    content: "\f121";
    }
        .fi-sr-angle-up:before {
    content: "\f122";
    }
        .fi-sr-angry:before {
    content: "\f123";
    }
        .fi-sr-apple-whole:before {
    content: "\f124";
    }
        .fi-sr-apps-add:before {
    content: "\f125";
    }
        .fi-sr-apps-delete:before {
    content: "\f126";
    }
        .fi-sr-apps-sort:before {
    content: "\f127";
    }
        .fi-sr-apps:before {
    content: "\f128";
    }
        .fi-sr-archive:before {
    content: "\f129";
    }
        .fi-sr-arrow-alt-circle-down:before {
    content: "\f12a";
    }
        .fi-sr-arrow-alt-circle-left:before {
    content: "\f12b";
    }
        .fi-sr-arrow-alt-circle-right:before {
    content: "\f12c";
    }
        .fi-sr-arrow-alt-circle-up:before {
    content: "\f12d";
    }
        .fi-sr-arrow-alt-down:before {
    content: "\f12e";
    }
        .fi-sr-arrow-alt-from-bottom:before {
    content: "\f12f";
    }
        .fi-sr-arrow-alt-from-left:before {
    content: "\f130";
    }
        .fi-sr-arrow-alt-from-right:before {
    content: "\f131";
    }
        .fi-sr-arrow-alt-from-top:before {
    content: "\f132";
    }
        .fi-sr-arrow-alt-left:before {
    content: "\f133";
    }
        .fi-sr-arrow-alt-right:before {
    content: "\f134";
    }
        .fi-sr-arrow-alt-square-down:before {
    content: "\f135";
    }
        .fi-sr-arrow-alt-square-left:before {
    content: "\f136";
    }
        .fi-sr-arrow-alt-square-right:before {
    content: "\f137";
    }
        .fi-sr-arrow-alt-square-up:before {
    content: "\f138";
    }
        .fi-sr-arrow-alt-to-bottom:before {
    content: "\f139";
    }
        .fi-sr-arrow-alt-to-left:before {
    content: "\f13a";
    }
        .fi-sr-arrow-alt-to-right:before {
    content: "\f13b";
    }
        .fi-sr-arrow-alt-to-top:before {
    content: "\f13c";
    }
        .fi-sr-arrow-alt-up:before {
    content: "\f13d";
    }
        .fi-sr-arrow-circle-down:before {
    content: "\f13e";
    }
        .fi-sr-arrow-circle-left:before {
    content: "\f13f";
    }
        .fi-sr-arrow-circle-right:before {
    content: "\f140";
    }
        .fi-sr-arrow-circle-up:before {
    content: "\f141";
    }
        .fi-sr-arrow-down:before {
    content: "\f142";
    }
        .fi-sr-arrow-from-bottom:before {
    content: "\f143";
    }
        .fi-sr-arrow-from-left:before {
    content: "\f144";
    }
        .fi-sr-arrow-from-right:before {
    content: "\f145";
    }
        .fi-sr-arrow-from-top:before {
    content: "\f146";
    }
        .fi-sr-arrow-left:before {
    content: "\f147";
    }
        .fi-sr-arrow-right:before {
    content: "\f148";
    }
        .fi-sr-arrow-small-down:before {
    content: "\f149";
    }
        .fi-sr-arrow-small-left:before {
    content: "\f14a";
    }
        .fi-sr-arrow-small-right:before {
    content: "\f14b";
    }
        .fi-sr-arrow-small-up:before {
    content: "\f14c";
    }
        .fi-sr-arrow-square-down:before {
    content: "\f14d";
    }
        .fi-sr-arrow-square-left:before {
    content: "\f14e";
    }
        .fi-sr-arrow-square-right:before {
    content: "\f14f";
    }
        .fi-sr-arrow-square-up:before {
    content: "\f150";
    }
        .fi-sr-arrow-to-bottom:before {
    content: "\f151";
    }
        .fi-sr-arrow-to-left:before {
    content: "\f152";
    }
        .fi-sr-arrow-to-right:before {
    content: "\f153";
    }
        .fi-sr-arrow-to-top:before {
    content: "\f154";
    }
        .fi-sr-arrow-up:before {
    content: "\f155";
    }
        .fi-sr-arrows-alt-h:before {
    content: "\f156";
    }
        .fi-sr-arrows-alt-v:before {
    content: "\f157";
    }
        .fi-sr-arrows-alt:before {
    content: "\f158";
    }
        .fi-sr-arrows-h-copy:before {
    content: "\f159";
    }
        .fi-sr-arrows-h:before {
    content: "\f15a";
    }
        .fi-sr-arrows:before {
    content: "\f15b";
    }
        .fi-sr-assept-document:before {
    content: "\f15c";
    }
        .fi-sr-assistive-listening-systems:before {
    content: "\f15d";
    }
        .fi-sr-asterik:before {
    content: "\f15e";
    }
        .fi-sr-at:before {
    content: "\f15f";
    }
        .fi-sr-aubergine:before {
    content: "\f160";
    }
        .fi-sr-avocado:before {
    content: "\f161";
    }
        .fi-sr-baby-carriage:before {
    content: "\f162";
    }
        .fi-sr-backpack:before {
    content: "\f163";
    }
        .fi-sr-bacon:before {
    content: "\f164";
    }
        .fi-sr-badge:before {
    content: "\f165";
    }
        .fi-sr-balloons:before {
    content: "\f166";
    }
        .fi-sr-ban:before {
    content: "\f167";
    }
        .fi-sr-band-aid:before {
    content: "\f168";
    }
        .fi-sr-bank:before {
    content: "\f169";
    }
        .fi-sr-barber-shop:before {
    content: "\f16a";
    }
        .fi-sr-baseball-alt:before {
    content: "\f16b";
    }
        .fi-sr-baseball:before {
    content: "\f16c";
    }
        .fi-sr-basketball-hoop:before {
    content: "\f16d";
    }
        .fi-sr-basketball:before {
    content: "\f16e";
    }
        .fi-sr-bed:before {
    content: "\f16f";
    }
        .fi-sr-beer:before {
    content: "\f170";
    }
        .fi-sr-bell-ring:before {
    content: "\f171";
    }
        .fi-sr-bell-school:before {
    content: "\f172";
    }
        .fi-sr-bell:before {
    content: "\f173";
    }
        .fi-sr-bike:before {
    content: "\f174";
    }
        .fi-sr-biking-mountain:before {
    content: "\f175";
    }
        .fi-sr-biking:before {
    content: "\f176";
    }
        .fi-sr-billiard:before {
    content: "\f177";
    }
        .fi-sr-bold:before {
    content: "\f178";
    }
        .fi-sr-bolt:before {
    content: "\f179";
    }
        .fi-sr-book-alt:before {
    content: "\f17a";
    }
        .fi-sr-book:before {
    content: "\f17b";
    }
        .fi-sr-bookmark:before {
    content: "\f17c";
    }
        .fi-sr-bottle:before {
    content: "\f17d";
    }
        .fi-sr-bow-arrow:before {
    content: "\f17e";
    }
        .fi-sr-bowling-ball:before {
    content: "\f17f";
    }
        .fi-sr-bowling-pins:before {
    content: "\f180";
    }
        .fi-sr-bowling:before {
    content: "\f181";
    }
        .fi-sr-box-alt:before {
    content: "\f182";
    }
        .fi-sr-box:before {
    content: "\f183";
    }
        .fi-sr-boxing-glove:before {
    content: "\f184";
    }
        .fi-sr-braille:before {
    content: "\f185";
    }
        .fi-sr-bread-slice:before {
    content: "\f186";
    }
        .fi-sr-bread:before {
    content: "\f187";
    }
        .fi-sr-briefcase:before {
    content: "\f188";
    }
        .fi-sr-broccoli:before {
    content: "\f189";
    }
        .fi-sr-broom:before {
    content: "\f18a";
    }
        .fi-sr-browser:before {
    content: "\f18b";
    }
        .fi-sr-brush:before {
    content: "\f18c";
    }
        .fi-sr-bug:before {
    content: "\f18d";
    }
        .fi-sr-building:before {
    content: "\f18e";
    }
        .fi-sr-bulb:before {
    content: "\f18f";
    }
        .fi-sr-burrito:before {
    content: "\f190";
    }
        .fi-sr-bus-alt:before {
    content: "\f191";
    }
        .fi-sr-bus:before {
    content: "\f192";
    }
        .fi-sr-butterfly:before {
    content: "\f193";
    }
        .fi-sr-cake-birthday:before {
    content: "\f194";
    }
        .fi-sr-cake-wedding:before {
    content: "\f195";
    }
        .fi-sr-calculator:before {
    content: "\f196";
    }
        .fi-sr-calendar-check:before {
    content: "\f197";
    }
        .fi-sr-calendar-clock:before {
    content: "\f198";
    }
        .fi-sr-calendar-exclamation:before {
    content: "\f199";
    }
        .fi-sr-calendar-lines-pen:before {
    content: "\f19a";
    }
        .fi-sr-calendar-lines:before {
    content: "\f19b";
    }
        .fi-sr-calendar-minus:before {
    content: "\f19c";
    }
        .fi-sr-calendar-pen:before {
    content: "\f19d";
    }
        .fi-sr-calendar-plus:before {
    content: "\f19e";
    }
        .fi-sr-calendar:before {
    content: "\f19f";
    }
        .fi-sr-call-history:before {
    content: "\f1a0";
    }
        .fi-sr-call-incoming:before {
    content: "\f1a1";
    }
        .fi-sr-call-missed:before {
    content: "\f1a2";
    }
        .fi-sr-call-outgoing:before {
    content: "\f1a3";
    }
        .fi-sr-camera:before {
    content: "\f1a4";
    }
        .fi-sr-camping:before {
    content: "\f1a5";
    }
        .fi-sr-candy-alt:before {
    content: "\f1a6";
    }
        .fi-sr-candy:before {
    content: "\f1a7";
    }
        .fi-sr-canned-food:before {
    content: "\f1a8";
    }
        .fi-sr-car-alt:before {
    content: "\f1a9";
    }
        .fi-sr-car-battery:before {
    content: "\f1aa";
    }
        .fi-sr-car-building:before {
    content: "\f1ab";
    }
        .fi-sr-car-bump:before {
    content: "\f1ac";
    }
        .fi-sr-car-bus:before {
    content: "\f1ad";
    }
        .fi-sr-car-crash:before {
    content: "\f1ae";
    }
        .fi-sr-car-garage:before {
    content: "\f1af";
    }
        .fi-sr-car-mechanic:before {
    content: "\f1b0";
    }
        .fi-sr-car-side:before {
    content: "\f1b1";
    }
        .fi-sr-car-tilt:before {
    content: "\f1b2";
    }
        .fi-sr-car-wash:before {
    content: "\f1b3";
    }
        .fi-sr-car:before {
    content: "\f1b4";
    }
        .fi-sr-caravan-alt:before {
    content: "\f1b5";
    }
        .fi-sr-caravan:before {
    content: "\f1b6";
    }
        .fi-sr-caret-circle-down:before {
    content: "\f1b7";
    }
        .fi-sr-caret-circle-right:before {
    content: "\f1b8";
    }
        .fi-sr-caret-circle-up:before {
    content: "\f1b9";
    }
        .fi-sr-caret-down:before {
    content: "\f1ba";
    }
        .fi-sr-caret-left:before {
    content: "\f1bb";
    }
        .fi-sr-caret-quare-up:before {
    content: "\f1bc";
    }
        .fi-sr-caret-right:before {
    content: "\f1bd";
    }
        .fi-sr-caret-square-down:before {
    content: "\f1be";
    }
        .fi-sr-caret-square-left_1:before {
    content: "\f1bf";
    }
        .fi-sr-caret-square-left:before {
    content: "\f1c0";
    }
        .fi-sr-caret-square-right:before {
    content: "\f1c1";
    }
        .fi-sr-caret-up:before {
    content: "\f1c2";
    }
        .fi-sr-carrot:before {
    content: "\f1c3";
    }
        .fi-sr-cars:before {
    content: "\f1c4";
    }
        .fi-sr-charging-station:before {
    content: "\f1c5";
    }
        .fi-sr-chart-connected:before {
    content: "\f1c6";
    }
        .fi-sr-chart-histogram:before {
    content: "\f1c7";
    }
        .fi-sr-chart-network:before {
    content: "\f1c8";
    }
        .fi-sr-chart-pie-alt:before {
    content: "\f1c9";
    }
        .fi-sr-chart-pie:before {
    content: "\f1ca";
    }
        .fi-sr-chart-pyramid:before {
    content: "\f1cb";
    }
        .fi-sr-chart-set-theory:before {
    content: "\f1cc";
    }
        .fi-sr-chart-tree:before {
    content: "\f1cd";
    }
        .fi-sr-chat-arrow-down:before {
    content: "\f1ce";
    }
        .fi-sr-chat-arrow-grow:before {
    content: "\f1cf";
    }
        .fi-sr-check:before {
    content: "\f1d0";
    }
        .fi-sr-checkbox:before {
    content: "\f1d1";
    }
        .fi-sr-cheese:before {
    content: "\f1d2";
    }
        .fi-sr-cherry:before {
    content: "\f1d3";
    }
        .fi-sr-chess-piece:before {
    content: "\f1d4";
    }
        .fi-sr-chess-bishop:before {
    content: "\f1d5";
    }
        .fi-sr-chess-board:before {
    content: "\f1d6";
    }
        .fi-sr-chess-clock-alt:before {
    content: "\f1d7";
    }
        .fi-sr-chess-clock:before {
    content: "\f1d8";
    }
        .fi-sr-chess-king-alt:before {
    content: "\f1d9";
    }
        .fi-sr-chess-king:before {
    content: "\f1da";
    }
        .fi-sr-chess-knight-alt:before {
    content: "\f1db";
    }
        .fi-sr-chess-knight:before {
    content: "\f1dc";
    }
        .fi-sr-chess-pawn-alt:before {
    content: "\f1dd";
    }
        .fi-sr-chess-pawn:before {
    content: "\f1de";
    }
        .fi-sr-chess-queen-alt:before {
    content: "\f1df";
    }
        .fi-sr-chess-queen:before {
    content: "\f1e0";
    }
        .fi-sr-chess-rook-alt:before {
    content: "\f1e1";
    }
        .fi-sr-chess-rook:before {
    content: "\f1e2";
    }
        .fi-sr-chess:before {
    content: "\f1e3";
    }
        .fi-sr-chevron-double-down:before {
    content: "\f1e4";
    }
        .fi-sr-chevron-double-up:before {
    content: "\f1e5";
    }
        .fi-sr-child-head:before {
    content: "\f1e6";
    }
        .fi-sr-chocolate:before {
    content: "\f1e7";
    }
        .fi-sr-circle-small:before {
    content: "\f1e8";
    }
        .fi-sr-circle:before {
    content: "\f1e9";
    }
        .fi-sr-clip:before {
    content: "\f1ea";
    }
        .fi-sr-clock-eight-thirty:before {
    content: "\f1eb";
    }
        .fi-sr-clock-eleven-thirty:before {
    content: "\f1ec";
    }
        .fi-sr-clock-eleven:before {
    content: "\f1ed";
    }
        .fi-sr-clock-five-thirty:before {
    content: "\f1ee";
    }
        .fi-sr-clock-five:before {
    content: "\f1ef";
    }
        .fi-sr-clock-four-thirty:before {
    content: "\f1f0";
    }
        .fi-sr-clock-nine-thirty:before {
    content: "\f1f1";
    }
        .fi-sr-clock-nine:before {
    content: "\f1f2";
    }
        .fi-sr-clock-one-thirty:before {
    content: "\f1f3";
    }
        .fi-sr-clock-one:before {
    content: "\f1f4";
    }
        .fi-sr-clock-seven-thirty:before {
    content: "\f1f5";
    }
        .fi-sr-clock-seven:before {
    content: "\f1f6";
    }
        .fi-sr-clock-six-thirty:before {
    content: "\f1f7";
    }
        .fi-sr-clock-six:before {
    content: "\f1f8";
    }
        .fi-sr-clock-ten-thirty:before {
    content: "\f1f9";
    }
        .fi-sr-clock-ten:before {
    content: "\f1fa";
    }
        .fi-sr-clock-three-thirty:before {
    content: "\f1fb";
    }
        .fi-sr-clock-three:before {
    content: "\f1fc";
    }
        .fi-sr-clock-twelve-thirty:before {
    content: "\f1fd";
    }
        .fi-sr-clock-twelve:before {
    content: "\f1fe";
    }
        .fi-sr-clock-two-thirty:before {
    content: "\f1ff";
    }
        .fi-sr-clock-two:before {
    content: "\f200";
    }
        .fi-sr-clock:before {
    content: "\f201";
    }
        .fi-sr-cloud-upload:before {
    content: "\f202";
    }
        .fi-sr-cloud-check:before {
    content: "\f203";
    }
        .fi-sr-cloud-disabled:before {
    content: "\f204";
    }
        .fi-sr-cloud-download-alt:before {
    content: "\f205";
    }
        .fi-sr-cloud-download:before {
    content: "\f206";
    }
        .fi-sr-cloud-drizzle:before {
    content: "\f207";
    }
        .fi-sr-cloud-hail-mixed:before {
    content: "\f208";
    }
        .fi-sr-cloud-hail:before {
    content: "\f209";
    }
        .fi-sr-cloud-moon-rain:before {
    content: "\f20a";
    }
        .fi-sr-cloud-moon:before {
    content: "\f20b";
    }
        .fi-sr-cloud-rain:before {
    content: "\f20c";
    }
        .fi-sr-cloud-rainbow:before {
    content: "\f20d";
    }
        .fi-sr-cloud-share:before {
    content: "\f20e";
    }
        .fi-sr-cloud-showers-heavy:before {
    content: "\f20f";
    }
        .fi-sr-cloud-showers:before {
    content: "\f210";
    }
        .fi-sr-cloud-sleet:before {
    content: "\f211";
    }
        .fi-sr-cloud-snow:before {
    content: "\f212";
    }
        .fi-sr-cloud-sun-rain:before {
    content: "\f213";
    }
        .fi-sr-cloud-sun:before {
    content: "\f214";
    }
        .fi-sr-cloud-upload-alt:before {
    content: "\f215";
    }
        .fi-sr-cloud:before {
    content: "\f216";
    }
        .fi-sr-clouds-moon:before {
    content: "\f217";
    }
        .fi-sr-clouds-sun:before {
    content: "\f218";
    }
        .fi-sr-clouds:before {
    content: "\f219";
    }
        .fi-sr-club:before {
    content: "\f21a";
    }
        .fi-sr-cocktail-alt:before {
    content: "\f21b";
    }
        .fi-sr-cocktail:before {
    content: "\f21c";
    }
        .fi-sr-coffee-pot:before {
    content: "\f21d";
    }
        .fi-sr-coffee:before {
    content: "\f21e";
    }
        .fi-sr-comment-alt:before {
    content: "\f21f";
    }
        .fi-sr-comment-check:before {
    content: "\f220";
    }
        .fi-sr-comment-heart:before {
    content: "\f221";
    }
        .fi-sr-comment-info:before {
    content: "\f222";
    }
        .fi-sr-comment-user:before {
    content: "\f223";
    }
        .fi-sr-comment:before {
    content: "\f224";
    }
        .fi-sr-comments:before {
    content: "\f225";
    }
        .fi-sr-compress-alt:before {
    content: "\f226";
    }
        .fi-sr-compress:before {
    content: "\f227";
    }
        .fi-sr-computer:before {
    content: "\f228";
    }
        .fi-sr-confetti:before {
    content: "\f229";
    }
        .fi-sr-cookie:before {
    content: "\f22a";
    }
        .fi-sr-copy-alt:before {
    content: "\f22b";
    }
        .fi-sr-copy:before {
    content: "\f22c";
    }
        .fi-sr-copyright:before {
    content: "\f22d";
    }
        .fi-sr-corn:before {
    content: "\f22e";
    }
        .fi-sr-cow:before {
    content: "\f22f";
    }
        .fi-sr-cream:before {
    content: "\f230";
    }
        .fi-sr-credit-card:before {
    content: "\f231";
    }
        .fi-sr-cricket:before {
    content: "\f232";
    }
        .fi-sr-croissant:before {
    content: "\f233";
    }
        .fi-sr-cross-circle:before {
    content: "\f234";
    }
        .fi-sr-cross-small:before {
    content: "\f235";
    }
        .fi-sr-cross:before {
    content: "\f236";
    }
        .fi-sr-crown:before {
    content: "\f237";
    }
        .fi-sr-cube:before {
    content: "\f238";
    }
        .fi-sr-cupcake:before {
    content: "\f239";
    }
        .fi-sr-curling:before {
    content: "\f23a";
    }
        .fi-sr-cursor-finger:before {
    content: "\f23b";
    }
        .fi-sr-cursor-plus:before {
    content: "\f23c";
    }
        .fi-sr-cursor-text-alt:before {
    content: "\f23d";
    }
        .fi-sr-cursor-text:before {
    content: "\f23e";
    }
        .fi-sr-cursor:before {
    content: "\f23f";
    }
        .fi-sr-dart:before {
    content: "\f240";
    }
        .fi-sr-dashboard:before {
    content: "\f241";
    }
        .fi-sr-data-transfer:before {
    content: "\f242";
    }
        .fi-sr-database:before {
    content: "\f243";
    }
        .fi-sr-delete-document:before {
    content: "\f244";
    }
        .fi-sr-delete-user:before {
    content: "\f245";
    }
        .fi-sr-delete:before {
    content: "\f246";
    }
        .fi-sr-dewpoint:before {
    content: "\f247";
    }
        .fi-sr-diamond:before {
    content: "\f248";
    }
        .fi-sr-dice-alt:before {
    content: "\f249";
    }
        .fi-sr-dice-d10:before {
    content: "\f24a";
    }
        .fi-sr-dice-d12:before {
    content: "\f24b";
    }
        .fi-sr-dice-d20:before {
    content: "\f24c";
    }
        .fi-sr-dice-d4:before {
    content: "\f24d";
    }
        .fi-sr-dice-d6:before {
    content: "\f24e";
    }
        .fi-sr-dice-d8:before {
    content: "\f24f";
    }
        .fi-sr-dice-four:before {
    content: "\f250";
    }
        .fi-sr-dice-one:before {
    content: "\f251";
    }
        .fi-sr-dice-six:before {
    content: "\f252";
    }
        .fi-sr-dice-three:before {
    content: "\f253";
    }
        .fi-sr-dice-two:before {
    content: "\f254";
    }
        .fi-sr-dice:before {
    content: "\f255";
    }
        .fi-sr-diploma:before {
    content: "\f256";
    }
        .fi-sr-disco-ball:before {
    content: "\f257";
    }
        .fi-sr-disk:before {
    content: "\f258";
    }
        .fi-sr-dizzy:before {
    content: "\f259";
    }
        .fi-sr-doctor:before {
    content: "\f25a";
    }
        .fi-sr-document-signed:before {
    content: "\f25b";
    }
        .fi-sr-document:before {
    content: "\f25c";
    }
        .fi-sr-dollar:before {
    content: "\f25d";
    }
        .fi-sr-download:before {
    content: "\f25e";
    }
        .fi-sr-dreidel:before {
    content: "\f25f";
    }
        .fi-sr-drink-alt:before {
    content: "\f260";
    }
        .fi-sr-drumstick:before {
    content: "\f261";
    }
        .fi-sr-duplicate:before {
    content: "\f262";
    }
        .fi-sr-e-learning:before {
    content: "\f263";
    }
        .fi-sr-earnings:before {
    content: "\f264";
    }
        .fi-sr-eclipse-alt:before {
    content: "\f265";
    }
        .fi-sr-eclipse:before {
    content: "\f266";
    }
        .fi-sr-edit-alt:before {
    content: "\f267";
    }
        .fi-sr-edit:before {
    content: "\f268";
    }
        .fi-sr-egg-fried:before {
    content: "\f269";
    }
        .fi-sr-egg:before {
    content: "\f26a";
    }
        .fi-sr-engine-warning:before {
    content: "\f26b";
    }
        .fi-sr-envelope-ban:before {
    content: "\f26c";
    }
        .fi-sr-envelope-download:before {
    content: "\f26d";
    }
        .fi-sr-envelope-marker:before {
    content: "\f26e";
    }
        .fi-sr-envelope-open:before {
    content: "\f26f";
    }
        .fi-sr-envelope-plus:before {
    content: "\f270";
    }
        .fi-sr-envelope:before {
    content: "\f271";
    }
        .fi-sr-euro:before {
    content: "\f272";
    }
        .fi-sr-exchange-alt:before {
    content: "\f273";
    }
        .fi-sr-exchange:before {
    content: "\f274";
    }
        .fi-sr-exclamation:before {
    content: "\f275";
    }
        .fi-sr-expand-arrows-alt:before {
    content: "\f276";
    }
        .fi-sr-expand-arrows:before {
    content: "\f277";
    }
        .fi-sr-expand:before {
    content: "\f278";
    }
        .fi-sr-eye-crossed:before {
    content: "\f279";
    }
        .fi-sr-eye-dropper:before {
    content: "\f27a";
    }
        .fi-sr-eye:before {
    content: "\f27b";
    }
        .fi-sr-feather:before {
    content: "\f27c";
    }
        .fi-sr-ferris-wheel:before {
    content: "\f27d";
    }
        .fi-sr-field-hockey:before {
    content: "\f27e";
    }
        .fi-sr-fighter-jet:before {
    content: "\f27f";
    }
        .fi-sr-file-ai:before {
    content: "\f280";
    }
        .fi-sr-file-eps:before {
    content: "\f281";
    }
        .fi-sr-file-psd:before {
    content: "\f282";
    }
        .fi-sr-file:before {
    content: "\f283";
    }
        .fi-sr-fill:before {
    content: "\f284";
    }
        .fi-sr-film:before {
    content: "\f285";
    }
        .fi-sr-filter:before {
    content: "\f286";
    }
        .fi-sr-fingerprint:before {
    content: "\f287";
    }
        .fi-sr-fish:before {
    content: "\f288";
    }
        .fi-sr-flag:before {
    content: "\f289";
    }
        .fi-sr-flame:before {
    content: "\f28a";
    }
        .fi-sr-flip-horizontal:before {
    content: "\f28b";
    }
        .fi-sr-flower-bouquet:before {
    content: "\f28c";
    }
        .fi-sr-flower-tulip:before {
    content: "\f28d";
    }
        .fi-sr-flower:before {
    content: "\f28e";
    }
        .fi-sr-flushed:before {
    content: "\f28f";
    }
        .fi-sr-fog:before {
    content: "\f290";
    }
        .fi-sr-folder:before {
    content: "\f291";
    }
        .fi-sr-following:before {
    content: "\f292";
    }
        .fi-sr-football:before {
    content: "\f293";
    }
        .fi-sr-fork:before {
    content: "\f294";
    }
        .fi-sr-form:before {
    content: "\f295";
    }
        .fi-sr-forward:before {
    content: "\f296";
    }
        .fi-sr-fox:before {
    content: "\f297";
    }
        .fi-sr-french-fries:before {
    content: "\f298";
    }
        .fi-sr-frown:before {
    content: "\f299";
    }
        .fi-sr-ftp:before {
    content: "\f29a";
    }
        .fi-sr-gallery:before {
    content: "\f29b";
    }
        .fi-sr-game-board-alt:before {
    content: "\f29c";
    }
        .fi-sr-gamepad:before {
    content: "\f29d";
    }
        .fi-sr-garage-car:before {
    content: "\f29e";
    }
        .fi-sr-garage-open:before {
    content: "\f29f";
    }
        .fi-sr-garage:before {
    content: "\f2a0";
    }
        .fi-sr-garlic:before {
    content: "\f2a1";
    }
        .fi-sr-gas-pump-alt:before {
    content: "\f2a2";
    }
        .fi-sr-gas-pump-slash:before {
    content: "\f2a3";
    }
        .fi-sr-gas-pump:before {
    content: "\f2a4";
    }
        .fi-sr-gem:before {
    content: "\f2a5";
    }
        .fi-sr-gif:before {
    content: "\f2a6";
    }
        .fi-sr-gift:before {
    content: "\f2a7";
    }
        .fi-sr-gingerbread-man:before {
    content: "\f2a8";
    }
        .fi-sr-glass-cheers:before {
    content: "\f2a9";
    }
        .fi-sr-glass:before {
    content: "\f2aa";
    }
        .fi-sr-glasses:before {
    content: "\f2ab";
    }
        .fi-sr-globe-alt:before {
    content: "\f2ac";
    }
        .fi-sr-globe:before {
    content: "\f2ad";
    }
        .fi-sr-golf-ball:before {
    content: "\f2ae";
    }
        .fi-sr-golf-club:before {
    content: "\f2af";
    }
        .fi-sr-golf:before {
    content: "\f2b0";
    }
        .fi-sr-graduation-cap:before {
    content: "\f2b1";
    }
        .fi-sr-grape:before {
    content: "\f2b2";
    }
        .fi-sr-graphic-tablet:before {
    content: "\f2b3";
    }
        .fi-sr-grid-alt:before {
    content: "\f2b4";
    }
        .fi-sr-grid:before {
    content: "\f2b5";
    }
        .fi-sr-grill:before {
    content: "\f2b6";
    }
        .fi-sr-grimace:before {
    content: "\f2b7";
    }
        .fi-sr-grin-alt:before {
    content: "\f2b8";
    }
        .fi-sr-grin-beam-sweat:before {
    content: "\f2b9";
    }
        .fi-sr-grin-beam:before {
    content: "\f2ba";
    }
        .fi-sr-grin-hearts:before {
    content: "\f2bb";
    }
        .fi-sr-grin-squint-tears:before {
    content: "\f2bc";
    }
        .fi-sr-grin-squint:before {
    content: "\f2bd";
    }
        .fi-sr-grin-stars:before {
    content: "\f2be";
    }
        .fi-sr-grin-tears:before {
    content: "\f2bf";
    }
        .fi-sr-grin-tongue-squint:before {
    content: "\f2c0";
    }
        .fi-sr-grin-tongue-wink:before {
    content: "\f2c1";
    }
        .fi-sr-grin-tongue:before {
    content: "\f2c2";
    }
        .fi-sr-grin-wink:before {
    content: "\f2c3";
    }
        .fi-sr-grin:before {
    content: "\f2c4";
    }
        .fi-sr-guitar:before {
    content: "\f2c5";
    }
        .fi-sr-gym:before {
    content: "\f2c6";
    }
        .fi-sr-hamburger-soda:before {
    content: "\f2c7";
    }
        .fi-sr-hamburger:before {
    content: "\f2c8";
    }
        .fi-sr-hand-holding-heart:before {
    content: "\f2c9";
    }
        .fi-sr-hastag:before {
    content: "\f2ca";
    }
        .fi-sr-hat-birthday:before {
    content: "\f2cb";
    }
        .fi-sr-hat-chef:before {
    content: "\f2cc";
    }
        .fi-sr-head-side-thinking:before {
    content: "\f2cd";
    }
        .fi-sr-headphones:before {
    content: "\f2ce";
    }
        .fi-sr-headset:before {
    content: "\f2cf";
    }
        .fi-sr-heart-arrow:before {
    content: "\f2d0";
    }
        .fi-sr-heart:before {
    content: "\f2d1";
    }
        .fi-sr-heat:before {
    content: "\f2d2";
    }
        .fi-sr-helicopter-side:before {
    content: "\f2d3";
    }
        .fi-sr-hiking:before {
    content: "\f2d4";
    }
        .fi-sr-hockey-mask:before {
    content: "\f2d5";
    }
        .fi-sr-hockey-puck:before {
    content: "\f2d6";
    }
        .fi-sr-hockey-sticks:before {
    content: "\f2d7";
    }
        .fi-sr-home-location-alt:before {
    content: "\f2d8";
    }
        .fi-sr-home-location:before {
    content: "\f2d9";
    }
        .fi-sr-home:before {
    content: "\f2da";
    }
        .fi-sr-hotdog:before {
    content: "\f2db";
    }
        .fi-sr-hourglass-end:before {
    content: "\f2dc";
    }
        .fi-sr-hourglass:before {
    content: "\f2dd";
    }
        .fi-sr-house-flood:before {
    content: "\f2de";
    }
        .fi-sr-humidity:before {
    content: "\f2df";
    }
        .fi-sr-hurricane:before {
    content: "\f2e0";
    }
        .fi-sr-ice-cream:before {
    content: "\f2e1";
    }
        .fi-sr-ice-skate:before {
    content: "\f2e2";
    }
        .fi-sr-id-badge:before {
    content: "\f2e3";
    }
        .fi-sr-inbox-in:before {
    content: "\f2e4";
    }
        .fi-sr-inbox-out:before {
    content: "\f2e5";
    }
        .fi-sr-inbox:before {
    content: "\f2e6";
    }
        .fi-sr-incognito:before {
    content: "\f2e7";
    }
        .fi-sr-indent:before {
    content: "\f2e8";
    }
        .fi-sr-infinity:before {
    content: "\f2e9";
    }
        .fi-sr-info:before {
    content: "\f2ea";
    }
        .fi-sr-interactive:before {
    content: "\f2eb";
    }
        .fi-sr-interlining:before {
    content: "\f2ec";
    }
        .fi-sr-interrogation:before {
    content: "\f2ed";
    }
        .fi-sr-italic:before {
    content: "\f2ee";
    }
        .fi-sr-jam:before {
    content: "\f2ef";
    }
        .fi-sr-jpg:before {
    content: "\f2f0";
    }
        .fi-sr-key:before {
    content: "\f2f1";
    }
        .fi-sr-keyboard:before {
    content: "\f2f2";
    }
        .fi-sr-kiss-beam:before {
    content: "\f2f3";
    }
        .fi-sr-kiss-wink-heart:before {
    content: "\f2f4";
    }
        .fi-sr-kiss:before {
    content: "\f2f5";
    }
        .fi-sr-kite:before {
    content: "\f2f6";
    }
        .fi-sr-knife:before {
    content: "\f2f7";
    }
        .fi-sr-label:before {
    content: "\f2f8";
    }
        .fi-sr-laptop:before {
    content: "\f2f9";
    }
        .fi-sr-lasso:before {
    content: "\f2fa";
    }
        .fi-sr-laugh-beam:before {
    content: "\f2fb";
    }
        .fi-sr-laugh-squint:before {
    content: "\f2fc";
    }
        .fi-sr-laugh-wink:before {
    content: "\f2fd";
    }
        .fi-sr-laugh:before {
    content: "\f2fe";
    }
        .fi-sr-layers:before {
    content: "\f2ff";
    }
        .fi-sr-layout-fluid:before {
    content: "\f300";
    }
        .fi-sr-leaf:before {
    content: "\f301";
    }
        .fi-sr-lemon:before {
    content: "\f302";
    }
        .fi-sr-letter-case:before {
    content: "\f303";
    }
        .fi-sr-lettuce:before {
    content: "\f304";
    }
        .fi-sr-level-down-alt:before {
    content: "\f305";
    }
        .fi-sr-level-down:before {
    content: "\f306";
    }
        .fi-sr-level-up-alt:before {
    content: "\f307";
    }
        .fi-sr-level-up:before {
    content: "\f308";
    }
        .fi-sr-life-ring:before {
    content: "\f309";
    }
        .fi-sr-line-width:before {
    content: "\f30a";
    }
        .fi-sr-link:before {
    content: "\f30b";
    }
        .fi-sr-lipstick:before {
    content: "\f30c";
    }
        .fi-sr-list-check:before {
    content: "\f30d";
    }
        .fi-sr-list:before {
    content: "\f30e";
    }
        .fi-sr-loading:before {
    content: "\f30f";
    }
        .fi-sr-location-alt:before {
    content: "\f310";
    }
        .fi-sr-lock-alt:before {
    content: "\f311";
    }
        .fi-sr-lock:before {
    content: "\f312";
    }
        .fi-sr-luchador:before {
    content: "\f313";
    }
        .fi-sr-luggage-rolling:before {
    content: "\f314";
    }
        .fi-sr-magic-wand:before {
    content: "\f315";
    }
        .fi-sr-makeup-brush:before {
    content: "\f316";
    }
        .fi-sr-man-head:before {
    content: "\f317";
    }
        .fi-sr-map-marker-cross:before {
    content: "\f318";
    }
        .fi-sr-map-marker-home:before {
    content: "\f319";
    }
        .fi-sr-map-marker-minus:before {
    content: "\f31a";
    }
        .fi-sr-map-marker-plus:before {
    content: "\f31b";
    }
        .fi-sr-map-marker:before {
    content: "\f31c";
    }
        .fi-sr-map:before {
    content: "\f31d";
    }
        .fi-sr-marker-time:before {
    content: "\f31e";
    }
        .fi-sr-marker:before {
    content: "\f31f";
    }
        .fi-sr-mars-double:before {
    content: "\f320";
    }
        .fi-sr-mars:before {
    content: "\f321";
    }
        .fi-sr-mask-carnival:before {
    content: "\f322";
    }
        .fi-sr-medicine:before {
    content: "\f323";
    }
        .fi-sr-megaphone:before {
    content: "\f324";
    }
        .fi-sr-meh-blank:before {
    content: "\f325";
    }
        .fi-sr-meh-rolling-eyes:before {
    content: "\f326";
    }
        .fi-sr-meh:before {
    content: "\f327";
    }
        .fi-sr-melon:before {
    content: "\f328";
    }
        .fi-sr-menu-burger:before {
    content: "\f329";
    }
        .fi-sr-menu-dots-vertical:before {
    content: "\f32a";
    }
        .fi-sr-menu-dots:before {
    content: "\f32b";
    }
        .fi-sr-meteor:before {
    content: "\f32c";
    }
        .fi-sr-microphone-alt:before {
    content: "\f32d";
    }
        .fi-sr-microphone:before {
    content: "\f32e";
    }
        .fi-sr-minus-small:before {
    content: "\f32f";
    }
        .fi-sr-minus:before {
    content: "\f330";
    }
        .fi-sr-mobile:before {
    content: "\f331";
    }
        .fi-sr-mode-landscape:before {
    content: "\f332";
    }
        .fi-sr-mode-portrait:before {
    content: "\f333";
    }
        .fi-sr-money:before {
    content: "\f334";
    }
        .fi-sr-moon-stars:before {
    content: "\f335";
    }
        .fi-sr-moon:before {
    content: "\f336";
    }
        .fi-sr-motorcycle:before {
    content: "\f337";
    }
        .fi-sr-mountains:before {
    content: "\f338";
    }
        .fi-sr-mouse:before {
    content: "\f339";
    }
        .fi-sr-mug-alt:before {
    content: "\f33a";
    }
        .fi-sr-mug-hot-alt:before {
    content: "\f33b";
    }
        .fi-sr-mug-hot:before {
    content: "\f33c";
    }
        .fi-sr-mug-tea:before {
    content: "\f33d";
    }
        .fi-sr-mug:before {
    content: "\f33e";
    }
        .fi-sr-mushroom:before {
    content: "\f33f";
    }
        .fi-sr-music-file:before {
    content: "\f340";
    }
        .fi-sr-music-alt:before {
    content: "\f341";
    }
        .fi-sr-music:before {
    content: "\f342";
    }
        .fi-sr-navigation:before {
    content: "\f343";
    }
        .fi-sr-network-cloud:before {
    content: "\f344";
    }
        .fi-sr-network:before {
    content: "\f345";
    }
        .fi-sr-noodles:before {
    content: "\f346";
    }
        .fi-sr-notebook:before {
    content: "\f347";
    }
        .fi-sr-oil-can:before {
    content: "\f348";
    }
        .fi-sr-oil-temp:before {
    content: "\f349";
    }
        .fi-sr-olive-oil:before {
    content: "\f34a";
    }
        .fi-sr-olives:before {
    content: "\f34b";
    }
        .fi-sr-onion:before {
    content: "\f34c";
    }
        .fi-sr-opacity:before {
    content: "\f34d";
    }
        .fi-sr-package:before {
    content: "\f34e";
    }
        .fi-sr-paint-brush:before {
    content: "\f34f";
    }
        .fi-sr-palette:before {
    content: "\f350";
    }
        .fi-sr-pan:before {
    content: "\f351";
    }
        .fi-sr-paper-plane:before {
    content: "\f352";
    }
        .fi-sr-password:before {
    content: "\f353";
    }
        .fi-sr-pause:before {
    content: "\f354";
    }
        .fi-sr-paw:before {
    content: "\f355";
    }
        .fi-sr-peach:before {
    content: "\f356";
    }
        .fi-sr-pencil:before {
    content: "\f357";
    }
        .fi-sr-pennant:before {
    content: "\f358";
    }
        .fi-sr-pepper-hot:before {
    content: "\f359";
    }
        .fi-sr-pepper:before {
    content: "\f35a";
    }
        .fi-sr-pharmacy:before {
    content: "\f35b";
    }
        .fi-sr-phone-call:before {
    content: "\f35c";
    }
        .fi-sr-phone-cross:before {
    content: "\f35d";
    }
        .fi-sr-phone-pause:before {
    content: "\f35e";
    }
        .fi-sr-phone-slash:before {
    content: "\f35f";
    }
        .fi-sr-physics:before {
    content: "\f360";
    }
        .fi-sr-picnic:before {
    content: "\f361";
    }
        .fi-sr-picture:before {
    content: "\f362";
    }
        .fi-sr-pie:before {
    content: "\f363";
    }
        .fi-sr-pineapple:before {
    content: "\f364";
    }
        .fi-sr-ping-pong:before {
    content: "\f365";
    }
        .fi-sr-pizza-slice:before {
    content: "\f366";
    }
        .fi-sr-plane-alt:before {
    content: "\f367";
    }
        .fi-sr-plane:before {
    content: "\f368";
    }
        .fi-sr-plate:before {
    content: "\f369";
    }
        .fi-sr-play-alt:before {
    content: "\f36a";
    }
        .fi-sr-play:before {
    content: "\f36b";
    }
        .fi-sr-playing-cards:before {
    content: "\f36c";
    }
        .fi-sr-plus-small:before {
    content: "\f36d";
    }
        .fi-sr-plus:before {
    content: "\f36e";
    }
        .fi-sr-poker-chip:before {
    content: "\f36f";
    }
        .fi-sr-poo:before {
    content: "\f370";
    }
        .fi-sr-popcorn:before {
    content: "\f371";
    }
        .fi-sr-portrait:before {
    content: "\f372";
    }
        .fi-sr-pot:before {
    content: "\f373";
    }
        .fi-sr-pound:before {
    content: "\f374";
    }
        .fi-sr-power:before {
    content: "\f375";
    }
        .fi-sr-presentation:before {
    content: "\f376";
    }
        .fi-sr-print:before {
    content: "\f377";
    }
        .fi-sr-protractor:before {
    content: "\f378";
    }
        .fi-sr-pulse:before {
    content: "\f379";
    }
        .fi-sr-pumpkin:before {
    content: "\f37a";
    }
        .fi-sr-puzzle-piece:before {
    content: "\f37b";
    }
        .fi-sr-pyramid:before {
    content: "\f37c";
    }
        .fi-sr-quote-right:before {
    content: "\f37d";
    }
        .fi-sr-radish:before {
    content: "\f37e";
    }
        .fi-sr-rainbow:before {
    content: "\f37f";
    }
        .fi-sr-raindrops:before {
    content: "\f380";
    }
        .fi-sr-raquet:before {
    content: "\f381";
    }
        .fi-sr-rec:before {
    content: "\f382";
    }
        .fi-sr-receipt:before {
    content: "\f383";
    }
        .fi-sr-record-vinyl:before {
    content: "\f384";
    }
        .fi-sr-rectabgle-vertical:before {
    content: "\f385";
    }
        .fi-sr-rectangle-horizontal:before {
    content: "\f386";
    }
        .fi-sr-rectangle-panoramic:before {
    content: "\f387";
    }
        .fi-sr-recycle:before {
    content: "\f388";
    }
        .fi-sr-redo-alt:before {
    content: "\f389";
    }
        .fi-sr-redo:before {
    content: "\f38a";
    }
        .fi-sr-reflect:before {
    content: "\f38b";
    }
        .fi-sr-refresh:before {
    content: "\f38c";
    }
        .fi-sr-remove-user:before {
    content: "\f38d";
    }
        .fi-sr-reply-all:before {
    content: "\f38e";
    }
        .fi-sr-resize:before {
    content: "\f38f";
    }
        .fi-sr-resources:before {
    content: "\f390";
    }
        .fi-sr-restaurant:before {
    content: "\f391";
    }
        .fi-sr-rewind:before {
    content: "\f392";
    }
        .fi-sr-rhombus:before {
    content: "\f393";
    }
        .fi-sr-rings-wedding:before {
    content: "\f394";
    }
        .fi-sr-road:before {
    content: "\f395";
    }
        .fi-sr-rocket-lunch:before {
    content: "\f396";
    }
        .fi-sr-rocket:before {
    content: "\f397";
    }
        .fi-sr-room-service:before {
    content: "\f398";
    }
        .fi-sr-rotate-right:before {
    content: "\f399";
    }
        .fi-sr-rugby-helmet:before {
    content: "\f39a";
    }
        .fi-sr-rugby:before {
    content: "\f39b";
    }
        .fi-sr-running:before {
    content: "\f39c";
    }
        .fi-sr-rv:before {
    content: "\f39d";
    }
        .fi-sr-sad-cry:before {
    content: "\f39e";
    }
        .fi-sr-sad-tear:before {
    content: "\f39f";
    }
        .fi-sr-sad:before {
    content: "\f3a0";
    }
        .fi-sr-salad:before {
    content: "\f3a1";
    }
        .fi-sr-salt-pepper:before {
    content: "\f3a2";
    }
        .fi-sr-sandwich:before {
    content: "\f3a3";
    }
        .fi-sr-sauce:before {
    content: "\f3a4";
    }
        .fi-sr-sausage:before {
    content: "\f3a5";
    }
        .fi-sr-scale:before {
    content: "\f3a6";
    }
        .fi-sr-school-bus:before {
    content: "\f3a7";
    }
        .fi-sr-school:before {
    content: "\f3a8";
    }
        .fi-sr-scissors:before {
    content: "\f3a9";
    }
        .fi-sr-screen:before {
    content: "\f3aa";
    }
        .fi-sr-search-alt:before {
    content: "\f3ab";
    }
        .fi-sr-search-heart:before {
    content: "\f3ac";
    }
        .fi-sr-search:before {
    content: "\f3ad";
    }
        .fi-sr-settings-sliders:before {
    content: "\f3ae";
    }
        .fi-sr-settings:before {
    content: "\f3af";
    }
        .fi-sr-share:before {
    content: "\f3b0";
    }
        .fi-sr-shield-check:before {
    content: "\f3b1";
    }
        .fi-sr-shield-exclamation:before {
    content: "\f3b2";
    }
        .fi-sr-shield-interrogation:before {
    content: "\f3b3";
    }
        .fi-sr-shield-plus:before {
    content: "\f3b4";
    }
        .fi-sr-shield:before {
    content: "\f3b5";
    }
        .fi-sr-ship-side:before {
    content: "\f3b6";
    }
        .fi-sr-ship:before {
    content: "\f3b7";
    }
        .fi-sr-shop:before {
    content: "\f3b8";
    }
        .fi-sr-shopping-bag-add:before {
    content: "\f3b9";
    }
        .fi-sr-shopping-bag:before {
    content: "\f3ba";
    }
        .fi-sr-shopping-cart-add:before {
    content: "\f3bb";
    }
        .fi-sr-shopping-cart-check:before {
    content: "\f3bc";
    }
        .fi-sr-shopping-cart:before {
    content: "\f3bd";
    }
        .fi-sr-shrimp:before {
    content: "\f3be";
    }
        .fi-sr-shuffle:before {
    content: "\f3bf";
    }
        .fi-sr-shuttle-van:before {
    content: "\f3c0";
    }
        .fi-sr-shuttlecock:before {
    content: "\f3c1";
    }
        .fi-sr-sign-in-alt:before {
    content: "\f3c2";
    }
        .fi-sr-sign-in:before {
    content: "\f3c3";
    }
        .fi-sr-sign-out-alt:before {
    content: "\f3c4";
    }
        .fi-sr-sign-out:before {
    content: "\f3c5";
    }
        .fi-sr-signal-alt-1:before {
    content: "\f3c6";
    }
        .fi-sr-signal-alt-2:before {
    content: "\f3c7";
    }
        .fi-sr-signal-alt:before {
    content: "\f3c8";
    }
        .fi-sr-skateboard:before {
    content: "\f3c9";
    }
        .fi-sr-skating:before {
    content: "\f3ca";
    }
        .fi-sr-skewer:before {
    content: "\f3cb";
    }
        .fi-sr-ski-jump:before {
    content: "\f3cc";
    }
        .fi-sr-ski-lift:before {
    content: "\f3cd";
    }
        .fi-sr-skiing-nordic:before {
    content: "\f3ce";
    }
        .fi-sr-skiing:before {
    content: "\f3cf";
    }
        .fi-sr-sledding:before {
    content: "\f3d0";
    }
        .fi-sr-sleigh:before {
    content: "\f3d1";
    }
        .fi-sr-smartphone:before {
    content: "\f3d2";
    }
        .fi-sr-smile-beam:before {
    content: "\f3d3";
    }
        .fi-sr-smile-wink:before {
    content: "\f3d4";
    }
        .fi-sr-smile:before {
    content: "\f3d5";
    }
        .fi-sr-smog:before {
    content: "\f3d6";
    }
        .fi-sr-smoke:before {
    content: "\f3d7";
    }
        .fi-sr-snow-blowing:before {
    content: "\f3d8";
    }
        .fi-sr-snowboarding:before {
    content: "\f3d9";
    }
        .fi-sr-snowflake:before {
    content: "\f3da";
    }
        .fi-sr-snowflakes:before {
    content: "\f3db";
    }
        .fi-sr-snowmobile:before {
    content: "\f3dc";
    }
        .fi-sr-snowplow:before {
    content: "\f3dd";
    }
        .fi-sr-soap:before {
    content: "\f3de";
    }
        .fi-sr-sort-alpha-down-alt:before {
    content: "\f3df";
    }
        .fi-sr-sort-alpha-down:before {
    content: "\f3e0";
    }
        .fi-sr-sort-alpha-up-alt:before {
    content: "\f3e1";
    }
        .fi-sr-sort-alpha-up:before {
    content: "\f3e2";
    }
        .fi-sr-sort-alt:before {
    content: "\f3e3";
    }
        .fi-sr-sort-amount-down-alt:before {
    content: "\f3e4";
    }
        .fi-sr-sort-amount-down:before {
    content: "\f3e5";
    }
        .fi-sr-sort-amount-up-alt:before {
    content: "\f3e6";
    }
        .fi-sr-sort-amount-up:before {
    content: "\f3e7";
    }
        .fi-sr-sort-down:before {
    content: "\f3e8";
    }
        .fi-sr-sort-numeric-down-alt:before {
    content: "\f3e9";
    }
        .fi-sr-sort-numeric-down:before {
    content: "\f3ea";
    }
        .fi-sr-sort:before {
    content: "\f3eb";
    }
        .fi-sr-soup:before {
    content: "\f3ec";
    }
        .fi-sr-spa:before {
    content: "\f3ed";
    }
        .fi-sr-space-shuttle:before {
    content: "\f3ee";
    }
        .fi-sr-spade:before {
    content: "\f3ef";
    }
        .fi-sr-sparkles:before {
    content: "\f3f0";
    }
        .fi-sr-speaker:before {
    content: "\f3f1";
    }
        .fi-sr-sphere:before {
    content: "\f3f2";
    }
        .fi-sr-spinner:before {
    content: "\f3f3";
    }
        .fi-sr-spoon:before {
    content: "\f3f4";
    }
        .fi-sr-square-root:before {
    content: "\f3f5";
    }
        .fi-sr-square:before {
    content: "\f3f6";
    }
        .fi-sr-star-octogram:before {
    content: "\f3f7";
    }
        .fi-sr-star:before {
    content: "\f3f8";
    }
        .fi-sr-starfighter:before {
    content: "\f3f9";
    }
        .fi-sr-stars:before {
    content: "\f3fa";
    }
        .fi-sr-stats:before {
    content: "\f3fb";
    }
        .fi-sr-steak:before {
    content: "\f3fc";
    }
        .fi-sr-steering-wheel:before {
    content: "\f3fd";
    }
        .fi-sr-stethoscope:before {
    content: "\f3fe";
    }
        .fi-sr-sticker:before {
    content: "\f3ff";
    }
        .fi-sr-stop:before {
    content: "\f400";
    }
        .fi-sr-stopwatch:before {
    content: "\f401";
    }
        .fi-sr-strawberry:before {
    content: "\f402";
    }
        .fi-sr-subtitles:before {
    content: "\f403";
    }
        .fi-sr-subway:before {
    content: "\f404";
    }
        .fi-sr-sun:before {
    content: "\f405";
    }
        .fi-sr-sunrise-alt:before {
    content: "\f406";
    }
        .fi-sr-sunrise:before {
    content: "\f407";
    }
        .fi-sr-sunset:before {
    content: "\f408";
    }
        .fi-sr-surfing:before {
    content: "\f409";
    }
        .fi-sr-surprise:before {
    content: "\f40a";
    }
        .fi-sr-sushi:before {
    content: "\f40b";
    }
        .fi-sr-swimmer:before {
    content: "\f40c";
    }
        .fi-sr-sword:before {
    content: "\f40d";
    }
        .fi-sr-syringe:before {
    content: "\f40e";
    }
        .fi-sr-tablet:before {
    content: "\f40f";
    }
        .fi-sr-tachometer-alt-average:before {
    content: "\f410";
    }
        .fi-sr-tachometer-alt-fastest:before {
    content: "\f411";
    }
        .fi-sr-tachometer-alt-slow:before {
    content: "\f412";
    }
        .fi-sr-tachometer-alt-slowest:before {
    content: "\f413";
    }
        .fi-sr-tachometer-average:before {
    content: "\f414";
    }
        .fi-sr-tachometer-fast:before {
    content: "\f415";
    }
        .fi-sr-tachometer-fastest:before {
    content: "\f416";
    }
        .fi-sr-tachometer-slow:before {
    content: "\f417";
    }
        .fi-sr-tachometer-slowest:before {
    content: "\f418";
    }
        .fi-sr-tachometer:before {
    content: "\f419";
    }
        .fi-sr-taco:before {
    content: "\f41a";
    }
        .fi-sr-target:before {
    content: "\f41b";
    }
        .fi-sr-taxi:before {
    content: "\f41c";
    }
        .fi-sr-temperature-down:before {
    content: "\f41d";
    }
        .fi-sr-temperature-frigid:before {
    content: "\f41e";
    }
        .fi-sr-temperature-high:before {
    content: "\f41f";
    }
        .fi-sr-temperature-hot:before {
    content: "\f420";
    }
        .fi-sr-temperature-low:before {
    content: "\f421";
    }
        .fi-sr-temperature-up:before {
    content: "\f422";
    }
        .fi-sr-tennis:before {
    content: "\f423";
    }
        .fi-sr-terrace:before {
    content: "\f424";
    }
        .fi-sr-test-tube:before {
    content: "\f425";
    }
        .fi-sr-test:before {
    content: "\f426";
    }
        .fi-sr-text-check:before {
    content: "\f427";
    }
        .fi-sr-text:before {
    content: "\f428";
    }
        .fi-sr-thermometer-half:before {
    content: "\f429";
    }
        .fi-sr-thumbs-down:before {
    content: "\f42a";
    }
        .fi-sr-thumbs-up:before {
    content: "\f42b";
    }
        .fi-sr-thumbtack:before {
    content: "\f42c";
    }
        .fi-sr-thunderstorm-moon:before {
    content: "\f42d";
    }
        .fi-sr-thunderstorm-sun:before {
    content: "\f42e";
    }
        .fi-sr-thunderstorm:before {
    content: "\f42f";
    }
        .fi-sr-ticket:before {
    content: "\f430";
    }
        .fi-sr-time-add:before {
    content: "\f431";
    }
        .fi-sr-time-check:before {
    content: "\f432";
    }
        .fi-sr-time-delete:before {
    content: "\f433";
    }
        .fi-sr-time-fast:before {
    content: "\f434";
    }
        .fi-sr-time-forward-sixty:before {
    content: "\f435";
    }
        .fi-sr-time-forward-ten:before {
    content: "\f436";
    }
        .fi-sr-time-forward:before {
    content: "\f437";
    }
        .fi-sr-time-half-past:before {
    content: "\f438";
    }
        .fi-sr-time-oclock:before {
    content: "\f439";
    }
        .fi-sr-time-past:before {
    content: "\f43a";
    }
        .fi-sr-time-quarter-to:before {
    content: "\f43b";
    }
        .fi-sr-time-quarter-past:before {
    content: "\f43c";
    }
        .fi-sr-time-twenty-four:before {
    content: "\f43d";
    }
        .fi-sr-tire-flat:before {
    content: "\f43e";
    }
        .fi-sr-tire-pressure-warning:before {
    content: "\f43f";
    }
        .fi-sr-tire-rugged:before {
    content: "\f440";
    }
        .fi-sr-tire:before {
    content: "\f441";
    }
        .fi-sr-tired:before {
    content: "\f442";
    }
        .fi-sr-tomato:before {
    content: "\f443";
    }
        .fi-sr-tool-crop:before {
    content: "\f444";
    }
        .fi-sr-tool-marquee:before {
    content: "\f445";
    }
        .fi-sr-tooth:before {
    content: "\f446";
    }
        .fi-sr-tornado:before {
    content: "\f447";
    }
        .fi-sr-tractor:before {
    content: "\f448";
    }
        .fi-sr-trailer:before {
    content: "\f449";
    }
        .fi-sr-train-side:before {
    content: "\f44a";
    }
        .fi-sr-train:before {
    content: "\f44b";
    }
        .fi-sr-tram:before {
    content: "\f44c";
    }
        .fi-sr-transform:before {
    content: "\f44d";
    }
        .fi-sr-trash:before {
    content: "\f44e";
    }
        .fi-sr-treatment:before {
    content: "\f44f";
    }
        .fi-sr-tree-christmas:before {
    content: "\f450";
    }
        .fi-sr-tree:before {
    content: "\f451";
    }
        .fi-sr-triangle:before {
    content: "\f452";
    }
        .fi-sr-trophy:before {
    content: "\f453";
    }
        .fi-sr-truck-container:before {
    content: "\f454";
    }
        .fi-sr-truck-couch:before {
    content: "\f455";
    }
        .fi-sr-truck-loading:before {
    content: "\f456";
    }
        .fi-sr-truck-monster:before {
    content: "\f457";
    }
        .fi-sr-truck-moving:before {
    content: "\f458";
    }
        .fi-sr-truck-pickup:before {
    content: "\f459";
    }
        .fi-sr-truck-plow:before {
    content: "\f45a";
    }
        .fi-sr-truck-ramp:before {
    content: "\f45b";
    }
        .fi-sr-truck-side:before {
    content: "\f45c";
    }
        .fi-sr-tty:before {
    content: "\f45d";
    }
        .fi-sr-turkey:before {
    content: "\f45e";
    }
        .fi-sr-umbrella:before {
    content: "\f45f";
    }
        .fi-sr-underline:before {
    content: "\f460";
    }
        .fi-sr-undo-alt:before {
    content: "\f461";
    }
        .fi-sr-undo:before {
    content: "\f462";
    }
        .fi-sr-unlock:before {
    content: "\f463";
    }
        .fi-sr-upload:before {
    content: "\f464";
    }
        .fi-sr-usb-pendrive:before {
    content: "\f465";
    }
        .fi-sr-user-add:before {
    content: "\f466";
    }
        .fi-sr-user-time:before {
    content: "\f467";
    }
        .fi-sr-user:before {
    content: "\f468";
    }
        .fi-sr-users-alt:before {
    content: "\f469";
    }
        .fi-sr-users:before {
    content: "\f46a";
    }
        .fi-sr-utensils:before {
    content: "\f46b";
    }
        .fi-sr-vector-alt:before {
    content: "\f46c";
    }
        .fi-sr-vector:before {
    content: "\f46d";
    }
        .fi-sr-venus-double:before {
    content: "\f46e";
    }
        .fi-sr-venus-mars:before {
    content: "\f46f";
    }
        .fi-sr-venus:before {
    content: "\f470";
    }
        .fi-sr-video-camera:before {
    content: "\f471";
    }
        .fi-sr-volcano:before {
    content: "\f472";
    }
        .fi-sr-volleyball:before {
    content: "\f473";
    }
        .fi-sr-volume:before {
    content: "\f474";
    }
        .fi-sr-wagon-covered:before {
    content: "\f475";
    }
        .fi-sr-water-bottle:before {
    content: "\f476";
    }
        .fi-sr-water-lower:before {
    content: "\f477";
    }
        .fi-sr-water-rise:before {
    content: "\f478";
    }
        .fi-sr-water:before {
    content: "\f479";
    }
        .fi-sr-watermelon:before {
    content: "\f47a";
    }
        .fi-sr-wheat:before {
    content: "\f47b";
    }
        .fi-sr-wheelchair:before {
    content: "\f47c";
    }
        .fi-sr-whistle:before {
    content: "\f47d";
    }
        .fi-sr-wifi-alt:before {
    content: "\f47e";
    }
        .fi-sr-wind-warning:before {
    content: "\f47f";
    }
        .fi-sr-wind:before {
    content: "\f480";
    }
        .fi-sr-windsock:before {
    content: "\f481";
    }
        .fi-sr-woman-head:before {
    content: "\f482";
    }
        .fi-sr-world:before {
    content: "\f483";
    }
        .fi-sr-yen:before {
    content: "\f484";
    }
        .fi-sr-zoom-in:before {
    content: "\f485";
    }
        .fi-sr-zoom-out:before {
    content: "\f486";
    }
