<?php
session_start();
error_reporting(0);
include ('admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');
$sel = dbQuery("SELECT tabl_user.*,tabl_plans.price,tabl_plans.daily_income FROM tabl_user INNER JOIN tabl_plans ON tabl_user.membership_id=tabl_plans.id WHERE tabl_user.membership_id!=0");
while ($res = dbFetchAssoc($sel)) {
	$user_id = $res['id'];
	$total_income = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res['id'] . "'");
	$res_total_income = dbFetchAssoc($total_income);
	// 	if ($res_total_income['total_earning'] < $res['earning_limit']) {

	// $total_downline = dbQuery("SELECT * FROM tabl_user WHERE upliner_id='" . $res['id'] . "' AND membership_id='" . $res['membership_id'] . "'");
	// $num_downline = dbNumRows($total_downline);

	// if ($num_downline > 1) {

	// 	if ($num_downline >= 2 && $num_downline < 4) {
	// 		$daily_income = (2 * $res['price'] * 0.25 / 100);
	// 		$daily_income = (float)$res['daily_income'] + (float)$daily_income;
	// 	} elseif ($num_downline == 100) {
	// 		$daily_income = (100 * $res['price'] * 0.25 / 100);
	// 		$daily_income = (float)$res['daily_income'] + (float)$daily_income;
	// 	}
	// } else {
	// 	$daily_income = $res['daily_income'];
	// }


	$price = $daily_income = $res['daily_income'];

	dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $user_id . "',receive_amount='" . $daily_income . "',type=1,type_id=3,narration='Daily Income',date_added='" . $date . "'");

	// starting code for calculating earnings from referrals and downlines
	//Refer Income

	//FIRST
	$sel_user = dbQuery("SELECT upliner_id FROM tabl_user WHERE id='" . $user_id . "'");
	$res_user = dbFetchAssoc($sel_user);

	$refer1 = dbQuery("SELECT id, upliner_id, earning_limit FROM tabl_user WHERE id='" . $res_user['upliner_id'] . "'");
	$num_refer1 = dbNumRows($refer1);

	if ($num_refer1 > 0) {
		$res_refer1 = dbFetchAssoc($refer1);
		$refer1_wallet = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_refer1['id'] . "'");
		$res_refer1_wallet = dbFetchAssoc($refer1_wallet);
		// echo "test";

		if ($res_refer1_wallet['total_earning'] < $res_refer1['earning_limit']) {

			// echo "test";
			$refer_bonus1 = $price * 15 / 100;

			dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer1['id'] . "',receive_amount='" . $refer_bonus1 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Daily Income',date_added='" . $date . "'");

			// echo "INSERT INTO tabl_main_wallet SET user_id='" . $res_refer1['upliner_id'] . "',receive_amount='" . $refer_bonus1 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Daily Income',date_added='" . $date . "' ";
		}

		// 		//END FIRST

		// 		//SECOND	

		$refer2 = dbQuery("SELECT id, upliner_id,earning_limit FROM tabl_user WHERE id='" . $res_refer1['upliner_id'] . "'");
		$num_refer2 = dbNumRows($refer2);
		if ($num_refer2 > 0) {
			$res_refer2 = dbFetchAssoc($refer2);
			$refer2_wallet = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_refer2['id'] . "'");
			$res_refer2_wallet = dbFetchAssoc($refer2_wallet);
			// 			if ($res_refer2_wallet['total_earning'] < $res_refer2['earning_limit']) {

			$refer_bonus2 = $price * 10 / 100;

			dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer2['id'] . "',receive_amount='" . $refer_bonus2 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Daily Income',date_added='" . $date . "'");
		}

		// // 			//END SECOND

		// // 			//THIRD


		$refer3 = dbQuery("SELECT id, upliner_id,earning_limit FROM tabl_user WHERE id='" . $res_refer2['upliner_id'] . "'");
		$num_refer3 = dbNumRows($refer3);
		if ($num_refer3 > 0) {
			$res_refer3 = dbFetchAssoc($refer3);
			$refer3_wallet = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_refer3['id'] . "'");
			$res_refer3_wallet = dbFetchAssoc($refer3_wallet);
			// if ($res_refer3_wallet['total_earning'] < $res_refer3['earning_limit']) {

			$refer_bonus3 = $price * 5 / 100;

			dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer3['id'] . "',receive_amount='" . $refer_bonus3 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Daily Income',date_added='" . $date . "'");
		}

		// // 			// 	//END THIRD

		// // 			// 	//FOURTH



		// // 			// 	// $refer4 = dbQuery("SELECT id, upliner_id,earning_limit FROM tabl_user WHERE id='" . $res_refer3['upliner_id'] . "'");
// // 			// 	// $num_refer4 = dbNumRows($refer4);
// // 			// 	// if ($num_refer4 > 0) {
// // 			// 	// 	$res_refer4 = dbFetchAssoc($refer4);
// // 			// 	// 	$refer4_wallet = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_refer3['upliner_id'] . "'");
// // 			// 	// 	$res_refer4_wallet = dbFetchAssoc($refer4_wallet);
// // 			// 	// 	if ($res_refer4_wallet['total_earning'] < $res_refer4['earning_limit']) {

		// // 			// 	// 		$refer_bonus4 = $price * 1 / 100;

		// // 			// 	// 		dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer4['upliner_id'] . "',receive_amount='" . $refer_bonus4 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Daily Income',date_added='" . $date . "'");
// // 			// 	// 	}
// // 			// 	// 	// END FOURTH
// // 			// 	// }
// // 			// }
// // 		}
	}
	//End REFER INCOME Here
	// ending code for calculating earnings from referrals and downlines

	// 	}
}