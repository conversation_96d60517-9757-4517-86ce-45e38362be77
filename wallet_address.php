<?php
@session_start();
include('./lib/auth.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

// echo $res['id'];
$wallet_balance = dbQuery("SELECT sum(receive_amount-paid_amount) as ttlamount FROM tabl_main_wallet WHERE user_id='" . $user['id'] . "'");
$res_wallet_amount = dbFetchAssoc($wallet_balance);

?>


<!doctype html>
<html lang="en">

<head>
    <meta cha$et="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Home</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <link rel="stylesheet" href="assets/nav.css">
    <style>
        .banner {
            /* border: 1px solid purple; */
            margin: 10px;
            background: #ecc7f1;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .line {
            padding: -4px;
            width: 100%;
            /* text-align: center; */
            /* justify-content: center; */
            height: 1px;
            background: black;
            margin-top: -4px;
        }

        .banner h6 {
            font-size: 15px;
            font-weight: 600;
        }

        .banner .fee {
            margin-top: -20px;
            font-weight: 500;
        }

        .fee p {
            font-style: italic;
        }

        input {
            border: none;
            border: 0.6 solid #bb3ca1;
            border-bottom: 2px solid #bea2d9;
            border-radius: 20px;
            height: 38px;


        }

        input:hover {
            border: 1 px solid #bea2d9;


        }

        .next {
            text-align: center;
        }

        .button {
            width: 100%;
            text-align: center;
            display: inline-block;
            padding: 10px 20px;
            text-decoration: none;
            background-color: #823fb5;
            color: #fff !important;
            border: none;
            border-radius: 20px;
        }


        .badge {
            font-weight: 600;
            line-height: 1.4;
            padding: 3px 6px;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease-out;
            -webkit-transition: all 0.3s ease-out;
        }

        .badge-success {
            color: #fff;
            background-color: #8dbf42;
        }

        .badge-warning {
            color: #fff;
            background-color: #e2a03f;
        }

        .badge-danger {
            color: #fff;
            background-color: #e7515a;
        }
    </style>
</head>

<body>
    <div class="fullbody1">
        <div class="header">
            <div class="header-items d-flex align-items-center justify-content-between m-2">
                <div class="web w-10" onclick="window.location.href = 'index.php'">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-arrow-left" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z" />
                    </svg>
                </div>

                <div class="home w-90 d-flex justify-content-center">
                    <h4>Wallet Address</h4>
                </div>

                <div class="web w-10" onclick="window.location.href = 'index.php'">
                    <!-- <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-headset" viewBox="0 0 16 16">
                        <path d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5"/>
                    </svg> -->
                </div>

            </div>
        </div>

        <?php
        $sel_network = dbQuery("SELECT * FROM tabl_bank_details WHERE `status`='0' AND user_id='" . $user['id'] . "'");
        if ($res_network = dbFetchAssoc($sel_network)) {
            $disabled = 'disabled';
            $link = '';
        } else {
            $disabled = '';
            $link = 'add_wallet_address.php';
        }
        ?>
        <div class="formdep m-3 my-2">
            <form action="">
                <div class="deposits d-flex flex-column mt-1">
                    <div class="next mt-1">
                        <a class="button <?php echo $disabled; ?>" href="<?php echo $link; ?>">Add New Address</a>
                    </div>
                </div>
            </form>
        </div>

        <?php
        $sel_network = dbQuery("SELECT * FROM tabl_bank_details WHERE user_id='" . $user['id'] . "'");
        while ($res_network = dbFetchAssoc($sel_network)) {

            if ($res_network['status'] == 1) {
                $status = '<span class="badge badge-success">Active</span>';
            } else if ($res_network['status'] == 2) {
                $status = '<span class="badge badge-danger">Inactive</span>';
            } else {
                $status = '<span class="badge badge-warning">Pending</span>';
            }

        ?>
            <div class="banner">
                <div class="row p-2">
                    <div class="col">
                        <h6>Status</h6>
                    </div>
                    <div class="col ">
                        <p class="text-end m-0"><?php echo $status; ?></p>
                    </div>
                </div>
                <div class="row px-2">
                    <div class="col">
                        <h6>Network Name</h6>
                    </div>
                    <div class="col ">
                        <p class="text-end m-0"><?php echo $res_network['network_name']; ?></p>
                    </div>
                </div>
                <div class="row px-2 ">
                    <div class="col">
                        <h6>Wallet Address</h6>
                    </div>
                    <div class="col ">
                        <p class="text-end m-0"><?php echo $res_network['wallet_address']; ?></p>
                    </div>
                </div>
            </div>
        <?php
        }
        ?>











    </div>




    <script src="assets/nav.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
</body>

</html>