<?php 
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page=6;
$sub_page=61;
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title><?php echo SITE; ?> - Fund Transfer to User Wallet </title>
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico"/>
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    
     <link rel="stylesheet" type="text/css" href="plugins/table/datatable/datatables.css">
    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/dt-global_style.css">
    
     <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/regular.css">
    <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/fontawesome.css">
</head>
<body class="alt-menu sidebar-noneoverflow">

  <?php include('inc/__header.php');?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include('inc/__menu.php');?>
        <!--  END TOPBAR  -->
        
        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">                
<nav class="breadcrumb-one" aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
        <li class="breadcrumb-item active"><a href="javascript:void(0);">Fund Transfer to User Wallet</a></li>

    </ol>
</nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-sm-12  layout-spacing">
                                
                        <div class="widget-content widget-content-area br-6">
                             <h4>Fund Transfer to User Wallet</h4>                         
                            <a href="add_user_fund_transfer.php"><button class="btn btn-primary mb-2">Add New</button></a>
                            <div class="table-responsive mb-4 mt-4">
                                <table id="zero-config" class="table table-hover" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>ID</th>                                            
                                            <th>Date</th>
                                            <th>Name</th>
                                            <th>SponsorID</th>
                                            <th>Added Fund</th> 
                                        </tr>
                                    </thead>
                                    <tbody>
                                       <?php 
									   $sel=dbQuery("SELECT tabl_tranfer_fund.*,tabl_user.name,tabl_user.sponsor_id FROM tabl_tranfer_fund INNER JOIN tabl_user ON tabl_tranfer_fund.user_id=tabl_user.id ORDER BY tabl_tranfer_fund.id DESC");
										$i=1;
										while($res=dbFetchAssoc($sel)){?> 
                                        <tr>
                                            <td><?php echo $i;?></td>
                                            <td><?php echo date('d-m-Y H:i:s',strtotime($res['date_added']));?></td>
                                            <td><div class="d-flex">
                                           <p class="align-self-center mb-0 admin-name"> <?php echo $res['name'];?> </p>
                                                </div></td>  
                                                <td><?php echo $res['sponsor_id'];?></td> 
                                                <td><?php echo $res['fund'];?></td>           
                                        </tr>
                                        <?php $i++; } ?>
                                    </tbody>                                    
                                </table>
                            </div>
                        </div>
                    </div>
                           </div>
                        </div>
                    </div>
               </div>
 <?php include('inc/__footer.php');?>
          </div>
          
        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->
 <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>
   
    <script>
        $(document).ready(function() {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL SCRIPTS -->
    <script src="plugins/table/datatable/datatables.js"></script>
    <script>
        $('#zero-config').DataTable({
            "oLanguage": {
                "oPaginate": { "sPrevious": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>', "sNext": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>' },
                "sInfo": "Showing page _PAGE_ of _PAGES_",
                "sSearch": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
                "sSearchPlaceholder": "Search...",
               "sLengthMenu": "Results :  _MENU_",
            },
            "stripeClasses": [],
            "lengthMenu": [8, 10, 20, 50],
            "pageLength": 10 
        });
    </script>
    <!-- END PAGE LEVEL SCRIPTS -->
<script>
    function delete_student(id)
	{
	var retVal = confirm("Are you sure want to delete.");
	if( retVal == true ){
      $.ajax({
	  url:'ajax/delete_student.php',
	  type:'post',
	  data:{'id':id},
	  success:function(data){
		  //alert(data);
		 if(data==1){
			 location.reload();
		  }
   		 },
 	 }); 
   }else{
        return false;
   }
 	
	}
    </script>
<script>
function change_status(tabl,val,row_id){
		var retVal = confirm("Are you sure want to change status.");
	if( retVal == true ){
      $.ajax({
	  url:'ajax/manage_activate.php',
	  type:'post',
	  data:{'tabl':tabl,'val':val,'row_id':row_id},
	  success:function(data){
		  //alert(data);
		 if(data==1){
			 location.reload();
		  }
   		 },
 	 }); 
   }else{
        return false;
   } 	
}
</script>
	
</body>
</html>