<?php
@session_start();
include('./lib/auth.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');


?>

<!doctype html>
<html lang="en">

<head>
  <meta cha$et="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Rule Description</title>
  <link rel="stylesheet" href="assets/style.css">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
  <link rel="stylesheet" href="assets/nav.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    .banner {
      /* border: 1px solid purple; */
      /* border: 1px solid purple;  */
      margin: 10px;
      background: #ffffff;
      /* border-radius: 8px; */
      box-shadow: 0 0 10px rgb(0 0 0 / 10%);
      padding: 12px;
    }

    .fullbody1 {
      background: #823fb50d;
      height: 180vh;
    }

    .icon {
      color: #823fb5;
      font-size: 38px;
      margin-bottom: 16px;
    }

    .line {
      padding: -4px;
      width: 100%;
      /* text-align: center; */
      /* justify-content: center; */
      height: 1px;
      background: black;
      margin-top: -4px;
    }

    .banner h6 {
      font-size: 15px;
      font-weight: 600;
    }


    .col {
      width: 207px;
      min-height: 40px;
    }

    .row {
      margin-bottom: -15px;
    }

    .next {
      position: absolute;
      bottom: 30px;
      text-align: center;
      justify-content: center;
    }

    .button {
      width: 90%;
      text-align: center;
      display: inline-block;
      padding: 10px 20px;
      text-decoration: none;
      background-color: orange;
      color: #fff !important;
      border: none;
      border-radius: 20px;
    }

    .next .contact {
      width: 90%;
      background: none;
      border: 1px solid #823fb5;
      color: #823fb5;
      margin-top: 20px;
    }

    .container {
      max-width: 100%;
      padding: 20px;
    }

    .rule-card {
      background-color: #fff;
      border-radius: 8px;
      padding: 20px;
      margin-top: 20px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .rule-card h3 {
      color: #333;
      font-size: 17px;
    }

    .rule-card p {
      color: #666;
    }
  </style>
</head>

<body>
  <div class="fullbody1">
    <div class="header">
      <div class="header-items d-flex align-items-center justify-content-between m-2">
        <div class="web w-10" onclick="window.location.href = 'index.php'">


          <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-arrow-left" viewBox="0 0 16 16">
            <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z" />
          </svg>
        </div>

        <div class="home w-90 d-flex justify-content-center">
          <h4>Rules</h4>
        </div>

        <div class="web w-10" onclick="window.location.href = 'index.php'">


          <!-- <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-headset" viewBox="0 0 16 16">
                        <path d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5"/>
                      </svg> -->
        </div>

      </div>
    </div>
    <div class="container">
      <h1 class="text-center mt-4" style="color: orange;">BTC Trade </h1>
      <p class="text-center">Here are some rules to follow</p>
      <div class="rule-card">
        <h3>1: Intellectual Property Rights</h3>
        <p>
          AI-generated creations may be subject to intellectual property laws. Ensure compliance with patents, copyrights, and trademarks.
        </p>
      </div>
      <div class="rule-card">
        <h3>2: Data Protection and Privacy Laws</h3>
        <p>
          Adhere to data protection laws governing the collection, processing, and storage of personal data used in AI-driven trade processes.
        </p>
      </div>
      <div class="rule-card">
        <h3>3: Trade Agreements and Standards</h3>
        <p>
          International trade agreements and standards bodies may address aspects related to AI in trade, such as technological standards, cross-border data flow, and market access. These agreements may indirectly influence AI trade practices.
        </p>
      </div>
      <div class="rule-card">
        <h3>5: Ethical Guidelines and Regulations</h3>
        <p>
          Some countries and organizations have proposed or implemented ethical guidelines or regulations concerning the use of AI. These guidelines aim to ensure fairness, transparency, and accountability in AI systems, which can have implications for AI in trade.
        </p>
      </div>
      <!-- More rule cards can be added as needed -->
    </div>
















  </div>




  <script src="assets/nav.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
</body>

</html>