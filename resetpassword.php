<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Registration Page</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        body {
            background-color: #e5dbed;
        }

        .registration-container {
            max-width: 300px;
            margin: 0 auto;
            padding: 20px;
            margin-top: 50px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff;
            border-radius: 8px;
        }

        .registration-container h2 {
            color: #823fb5;
            margin-bottom: 30px;
            text-align: center;
            font-size: 22px;
            font-weight: bold;
        }

        .form-group {
            position: relative;
            margin-bottom: 20px;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ced4da;
            padding-left: 40px;
            box-sizing: border-box;
        }

        .form-group i {
            position: absolute;
            left: 10px;
            top: 12px;
            color: #823fb5;
        }

        .checkbox-group {
            margin-top: 20px;
        }

        .checkbox-group label {
            display: block;
            position: relative;
            padding-left: 30px;
            cursor: pointer;
            font-size: 12px;
        }

        .checkbox-group input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }

        .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 20px;
            width: 20px;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 3px;
        }

        .checkbox-group input:checked~.checkmark {
            background-color: #823fb5;
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
        }

        .checkbox-group input:checked~.checkmark:after {
            display: block;
        }

        .checkbox-group .checkmark:after {
            left: 7px;
            top: 3px;
            width: 6px;
            height: 12px;
            border: solid white;
            border-width: 0 3px 3px 0;
            transform: rotate(45deg);
        }

        .register-link {
            text-align: center;
            margin-top: 15px;
            font-size: 13px;
        }

        .register-link a {
            color: #823fb5;
        }

        .btn-register {
            background-color: #823fb5;
            border-color: #823fb5;
            width: 100%;
        }

        .btn-register:hover {
            background: none;
            border: 1px solid #823fb5;
            color: #823fb5;
        }

        .head {
            font-size: 11px;
            text-align: center;
            margin-bottom: 21px;
            margin-top: -20px;

        }
    </style>
</head>

<body>
    <div class="container">
        <div class="registration-container">
            <h2>Reset password</h2>
            <p class="head">Enter your emai address to reset your password</p>
            <form>

                <div class="form-group">
                    <i class="fas fa-envelope"></i>
                    <input type="email" class="form-control" id="email" placeholder="Email">
                </div>

                <button type="submit" class="btn btn-primary btn-register">SEND CODE</button>
            </form>
            <div class="register-link">
                <p>Back to<a href="login.php">Login</a></p>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.0.7/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>

</html>