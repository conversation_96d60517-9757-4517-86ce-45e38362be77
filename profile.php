<?php
@session_start();
include('./lib/auth.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');



$sel = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
$res = dbFetchAssoc($sel);

$m_wallet = 0;

// echo $res['id'];
$wallet_balance = dbQuery("SELECT sum(receive_amount-paid_amount) as ttlamount FROM tabl_main_wallet WHERE user_id='" . $res['id'] . "'");
if ($res_wallet_amount = dbFetchAssoc($wallet_balance)) {
    if ($res_wallet_amount['ttlamount'] == "") {
        $m_wallet = 0;
    } else {
        $m_wallet = $res_wallet_amount['ttlamount'];
    }
}


$subscribe_plan_amount = 0;

$exist_plan = dbQuery("SELECT * FROM tabl_plans WHERE id='" . $res['membership_id'] . "'");

if ($res_exist_plan = dbFetchAssoc($exist_plan)) {
    include("./lib/upgrade.php");

    $subscribe_plan_amount = $res_exist_plan['price'];
}


?>

<!doctype html>
<html lang="en">

<head>
    <meta cha$et="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Profile page</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <link rel="stylesheet" href="assets/nav.css">
    <style>
        body {
            background: #823fb50d;
        }

        .banner {
            width: 100%;
            /* position: absolute; */

            /* background-color: #823fb5; */
            /* height: 114px; */
            z-index: -1;
            /* margin-top: -50px; */
        }

        .profile-card {
            max-width: 400px;
            /* margin-bottom: 30px; */
            margin: 42px;
            background-color: #ffffff;
            border-radius: 0px 30px 0px 30px;
            padding: 14px;
            margin-left: auto;
            margin-right: auto;
            margin-top: 23px;
            box-shadow: 0 0 10px rgb(20 22 179 / 10%);
        }

        .profile-photo {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            margin: 0 auto 20px;
        }

        .profile-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        h2,
        p {
            text-align: center;
            margin: 0;
            color: #6c757d;
            /* Dark Gray */
        }

        .container {
            /* position: absolute; */
            display: inline-block;
            margin: 0 auto;
            max-width: 1320px;
            width: 100%;
            /* top: 40%; */
        }

        .adjust-container {
            margin: 0 auto;
            display: flex;
            width: 100%;
            padding: 50px 0;
        }

        .redownload {
            background-color: #55c981 !important;
        }

        .redownload:hover {
            background-color: transparent !important;
            border: 1px solid #55c981;
            color: #55c981 !important;
        }

        .download {
            background-color: #ff6500;
            border: none;
            color: #fff;
            width: 50%;
            height: 39px;
            font-size: 15px;
            font-weight: 500;
            /* margin-top: -24px; */
            text-align: center;
            justify-content: center;
            align-items: center;
            border-radius: 20px 0px 20px 0px;
        }

        .apk {
            text-align: center;
            margin-top: 35px;
        }

        .popup-link {
            display: flex;
            flex-wrap: wrap;
        }

        .popup-link a {
            background: #333;
            color: #fff;
            padding: 10px 30px;
            border-radius: 5px;
            font-size: 17px;
            cursor: pointer;
            margin: 20px;
            text-decoration: none;
        }

        .popup-container {
            visibility: hidden;
            opacity: 0;
            transition: all 0.3s ease-in-out;
            transform: scale(1.3);
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(21, 17, 17, 0.61);
            display: flex;
            align-items: center;
        }

        .popup-content {
            background-color: #fefefe;
            margin: auto;
            padding: 12px;
            padding: -21px;
            border: 1px solid #888;
            width: 80%;
        }

        .popup-content p {
            text-align: left;
            font-size: 17px;
            padding: 10px;
            line-height: 20px;
        }

        .popup-content a.close {
            color: #aaaaaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            background: none;
            padding: 0;
            margin: 0;
            text-decoration: none;
        }

        .popup-content a.close:hover {
            color: #333;
        }

        .popup-content span:hover,
        .popup-content span:focus {
            color: #000;
            text-decoration: none;
            cursor: pointer;
        }

        .popup-container:target {
            visibility: visible;
            opacity: 1;
            transform: scale(1);
        }

        .popup-container h3 {
            margin: 10px;
        }

        .home img {
            vertical-align: middle;
            width: 21%;
            border-radius: 50%;
            height: 98%;
        }

        .header {
            background: #823fb5;
            padding: 1px;
        }

        .name {
            color: #fff;
            margin-left: 9px;
            line-height: 24px;
        }

        .name p {
            font-size: 13px;
        }

        .refresh p {
            font-size: 16px;
            margin-bottom: 21px;
        }

        .deposit-name p {
            margin: auto;
        }

        .fullbody1 {
            min-height: 100vh;
            height: auto;
        }
    </style>
</head>

<body>
    <div class="fullbody1">
        <div class="header">
            <div class="header-items d-flex align-items-center justify-content-between m-2">
                <div class="web w-10" onclick="window.location.href = 'index.php'">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-arrow-left"
                        viewBox="0 0 16 16">
                        <path fill-rule="evenodd"
                            d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z" />
                    </svg>
                </div>

                <div class="home w-90 d-flex justify-content-center">
                    <h4>Your Profile</h4>
                </div>

                <div class="web w-10 pointer px-2" onclick="window.location.href = 'change_password.php'">
                    <i class="bi bi-key"></i>
                </div>
                <div class="web w-10 pointer px-2" onclick="window.location.href = 'logout.php'">
                    <i class="bi bi-box-arrow-right"></i>

                    <!-- <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-headset" viewBox="0 0 16 16">
                        <path d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5" />
                    </svg> -->
                </div>

            </div>
        </div>


        <div class="banner">

            <div class="profile-card">
                <div class="profile-photo">
                    <!--<img src="./assets/image/profile_pic/<?php echo $user['user_image']; ?>" alt="Profile Photo">-->
                    <img src="./assets/image/logo2.png" alt="Profile Photo">
                </div>
                <h2>
                    <?php echo $user['name'] ?>
                </h2>
                <p>
                    <?php echo $user['email'] ?>
                </p>
            </div>
        </div>
        <div id="popup1" class="popup-container">
            <div class="popup-content">
                <a href="#" class="close">&times;</a>
                <h3>Invite Your Friends</h3>
                <p>Share the link below with your friends:</p>
                <div class="input-group mb-3">
                    <input type="text" class="form-control" id="invite-link"
                        value="<?php echo $base_link; ?>register.php?ref=<?php echo $user['sponsor_id']; ?>" readonly>
                    <button class="btn btn-primary" onclick="copyLink()">Copy Link</button>
                </div>
            </div>
        </div>
        <div class="adjust-container">
            <div class="container">
                <div class="py-3 ">

                    <div class="deposit-row container-fluid my-0">
                        <div class="d-flex justify-content-between  row">
                            <div class="deposit d-flex flex-column align-items-center col-4">
                                <a href="plan.php">
                                    <div class="icons">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-download" viewBox="0 0 16 16">
                                            <path
                                                d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z" />
                                            <path
                                                d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z" />
                                        </svg>
                                    </div>
                                </a>
                                <div class="deposit-name">
                                    <p>Deposit</p>
                                </div>
                            </div>
                            <div class="deposit d-flex flex-column align-items-center col-4">
                                <a href="withdraw.php">
                                    <div class="icons1">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-arrow-up-right" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd"
                                                d="M14 2.5a.5.5 0 0 0-.5-.5h-6a.5.5 0 0 0 0 1h4.793L2.146 13.146a.5.5 0 0 0 .708.708L13 3.707V8.5a.5.5 0 0 0 1 0v-6z" />
                                        </svg>
                                    </div>
                                </a>
                                <div class="deposit-name">
                                    <p>Withdraw</p>
                                </div>
                            </div>
                            <div class="deposit d-flex flex-column align-items-center col-4">
                                <div class="icons2">
                                    <a href="#popup1">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-people" viewBox="0 0 16 16">
                                            <path
                                                d="M15 14s1 0 1-1-1-4-5-4-5 3-5 4 1 1 1 1zm-7.978-1A.261.261 0 0 1 7 12.996c.001-.264.167-1.03.76-1.72C8.312 10.629 9.282 10 11 10c1.717 0 2.687.63 3.24 1.276.593.69.758 1.457.76 1.72l-.008.002a.274.274 0 0 1-.014.002H7.022ZM11 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4m3-2a3 3 0 1 1-6 0 3 3 0 0 1 6 0M6.936 9.28a5.88 5.88 0 0 0-1.23-.247A7.35 7.35 0 0 0 5 9c-4 0-5 3-5 4 0 .667.333 1 1 1h4.216A2.238 2.238 0 0 1 5 13c0-1.01.377-2.042 1.09-2.904.243-.294.526-.569.846-.816M4.92 10A5.493 5.493 0 0 0 4 13H1c0-.26.164-1.03.76-1.724.545-.636 1.492-1.256 3.16-1.275ZM1.5 5.5a3 3 0 1 1 6 0 3 3 0 0 1-6 0m3-2a2 2 0 1 0 0 4 2 2 0 0 0 0-4" />
                                        </svg>
                                    </a>
                                </div>
                                <div class="deposit-name">
                                    <p>Invite friends</p>
                                </div>
                            </div>
                            <!-- <div class="deposit d-flex flex-column align-items-center col-3">
                            <div class="icons3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-gift" viewBox="0 0 16 16">
                                    <path d="M3 2.5a2.5 2.5 0 0 1 5 0 2.5 2.5 0 0 1 5 0v.006c0 .07 0 .27-.038.494H15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 14.5V7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2.038A2.968 2.968 0 0 1 3 2.506V2.5zm1.068.5H7v-.5a1.5 1.5 0 1 0-3 0c0 .085.002.274.045.43a.522.522 0 0 0 .023.07zM9 3h2.932a.56.56 0 0 0 .023-.07c.043-.156.045-.345.045-.43a1.5 1.5 0 0 0-3 0V3zM1 4v2h6V4H1zm8 0v2h6V4H9zm5 3H9v8h4.5a.5.5 0 0 0 .5-.5V7zm-7 8V7H2v7.5a.5.5 0 0 0 .5.5H7z"/>
                                </svg>
                            </div>
                            <div class="deposit-name">
                                <p>Event</p>
                            </div>
                        </div> -->
                        </div>
                    </div>
                    <div class="deposit-row container-fluid my-0">
                        <div class="d-flex justify-content-between  row">
                            <div class="deposit d-flex flex-column align-items-center col-4">
                                <a href="about.php">
                                    <div class="icons4">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-info-circle" viewBox="0 0 16 16">
                                            <path
                                                d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
                                            <path
                                                d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z" />
                                        </svg>
                                    </div>
                                </a>
                                <div class="deposit-name">
                                    <p>About us</p>
                                </div>
                            </div>
                            <div class="deposit d-flex flex-column align-items-center col-4">
                                <a href="rule.php">
                                    <div class="icons5">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-chat" viewBox="0 0 16 16">
                                            <path
                                                d="M2.678 11.894a1 1 0 0 1 .287.801 10.97 10.97 0 0 1-.398 2c1.395-.323 2.247-.697 2.634-.893a1 1 0 0 1 .71-.074A8.06 8.06 0 0 0 8 14c3.996 0 7-2.807 7-6 0-3.192-3.004-6-7-6S1 4.808 1 8c0 1.468.617 2.83 1.678 3.894zm-.493 3.905a21.682 21.682 0 0 1-.713.129c-.2.032-.352-.176-.273-.362a9.68 9.68 0 0 0 .244-.637l.003-.01c.248-.72.45-1.548.524-2.319C.743 11.37 0 9.76 0 8c0-3.866 3.582-7 8-7s8 3.134 8 7-3.582 7-8 7a9.06 9.06 0 0 1-2.347-.306c-.52.263-1.639.742-3.468 1.105z" />
                                        </svg>
                                    </div>
                                </a>
                                <div class="deposit-name">
                                    <p>Rule description</p>
                                </div>
                            </div>
                            <!-- <div class="deposit d-flex flex-column align-items-center col-3">
                            <div class="icons6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-hand-thumbs-up" viewBox="0 0 16 16">
                                    <path d="M8.864.046C7.908-.193 7.02.53 6.956 1.466c-.072 1.051-.23 2.016-.428 2.59-.125.36-.479 1.013-1.04 1.639-.557.623-1.282 1.178-2.131 1.41C2.685 7.288 2 7.87 2 8.72v4.001c0 .845.682 1.464 1.448 1.545 1.07.114 1.564.415 2.068.723l.048.03c.272.165.578.348.97.484.397.136.861.217 1.466.217h3.5c.937 0 1.599-.477 1.934-1.064a1.86 1.86 0 0 0 .254-.912c0-.152-.023-.312-.077-.464.201-.263.38-.578.488-.901.11-.33.172-.762.004-1.149.069-.13.12-.269.159-.403.077-.27.113-.568.113-.857 0-.288-.036-.585-.113-.856a2.144 2.144 0 0 0-.138-.362 1.9 1.9 0 0 0 .234-1.734c-.206-.592-.682-1.1-1.2-1.272-.847-.282-1.803-.276-2.516-.211a9.84 9.84 0 0 0-.443.05 9.365 9.365 0 0 0-.062-4.509A1.38 1.38 0 0 0 9.125.111L8.864.046zM11.5 14.721H8c-.51 0-.863-.069-1.14-.164-.281-.097-.506-.228-.776-.393l-.04-.024c-.555-.339-1.198-.731-2.49-.868-.333-.036-.554-.29-.554-.55V8.72c0-.254.226-.543.62-.65 1.095-.3 1.977-.996 2.614-1.708.635-.71 1.064-1.475 1.238-1.978.243-.7.407-1.768.482-2.85.025-.362.36-.594.667-.518l.262.066c.16.04.258.143.288.255a8.34 8.34 0 0 1-.145 4.725.5.5 0 0 0 .595.644l.003-.001.014-.003.058-.014a8.908 8.908 0 0 1 1.036-.157c.663-.06 1.457-.054 2.11.**************.***********.308.087.67-.266 1.022l-.353.353.353.354c.***************.154.315.**************.075.581 0 .212-.027.414-.075.582-.05.174-.111.272-.154.315l-.353.353.353.354c.***************.005.488a2.224 2.224 0 0 1-.505.805l-.353.353.353.354c.**************.041.17a.866.866 0 0 1-.121.416c-.165.288-.503.56-1.066.56z"/>
                                </svg>
                            </div>
                            <div class="deposit-name">
                                <p>Promotion description</p>
                            </div>
                        </div> -->

                            <div class="deposit d-flex flex-column align-items-center col-4">
                                <a href="wallet_address.php">
                                    <div class="icons7">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#fff"
                                            class="bi bi-wallet2" viewBox="0 0 16 16">
                                            <path
                                                d="M12.136.326A1.5 1.5 0 0 1 14 1.78V3h.5A1.5 1.5 0 0 1 16 4.5v9a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 0 13.5v-9a1.5 1.5 0 0 1 1.432-1.499L12.136.326zM5.562 3H13V1.78a.5.5 0 0 0-.621-.484L5.562 3zM1.5 4a.5.5 0 0 0-.5.5v9a.5.5 0 0 0 .5.5h13a.5.5 0 0 0 .5-.5v-9a.5.5 0 0 0-.5-.5h-13z">
                                            </path>
                                        </svg>
                                    </div>
                                </a>
                                <div class="deposit-name">
                                    <p>Wallet Address</p>
                                </div>
                            </div>

                            <!-- <div class="deposit d-flex flex-column align-items-center col-3">
                            <a href="plan.php">
                                <div class="icons7">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-star" viewBox="0 0 16 16">
                                        <path d="M2.866 14.85c-.078.444.36.791.746.593l4.39-2.256 4.389 2.256c.386.198.824-.149.746-.592l-.83-4.73 3.522-3.356c.33-.314.16-.888-.282-.95l-4.898-.696L8.465.792a.513.513 0 0 0-.927 0L5.354 5.12l-4.898.696c-.441.062-.612.636-.283.95l3.523 3.356-.83 4.73zm4.905-2.767-3.686 1.894.694-3.957a.565.565 0 0 0-.163-.505L1.71 6.745l4.052-.576a.525.525 0 0 0 .393-.288L8 2.223l1.847 3.658a.525.525 0 0 0 .393.288l4.052.575-2.906 2.77a.565.565 0 0 0-.163.506l.694 3.957-3.686-1.894a.503.503 0 0 0-.461 0z" />
                                    </svg>
                                </div>
                            </a>
                            <div class="deposit-name">
                                <p>VIP
                                </p>
                            </div>
                        </div> -->
                        </div>
                    </div>

                    <div class="deposit-row container-fluid my-0">
                        <div class="d-flex justify-content-center row">
                            <div class="deposit d-flex flex-column align-items-center col-4">
                                <a href="team.php">
                                    <div class="icons5">
                                        <!-- <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-chat" viewBox="0 0 16 16">
                                            <path
                                                d="M2.678 11.894a1 1 0 0 1 .287.801 10.97 10.97 0 0 1-.398 2c1.395-.323 2.247-.697 2.634-.893a1 1 0 0 1 .71-.074A8.06 8.06 0 0 0 8 14c3.996 0 7-2.807 7-6 0-3.192-3.004-6-7-6S1 4.808 1 8c0 1.468.617 2.83 1.678 3.894zm-.493 3.905a21.682 21.682 0 0 1-.713.129c-.2.032-.352-.176-.273-.362a9.68 9.68 0 0 0 .244-.637l.003-.01c.248-.72.45-1.548.524-2.319C.743 11.37 0 9.76 0 8c0-3.866 3.582-7 8-7s8 3.134 8 7-3.582 7-8 7a9.06 9.06 0 0 1-2.347-.306c-.52.263-1.639.742-3.468 1.105z" />
                                        </svg> -->

                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-people" viewBox="0 0 16 16">
                                            <path
                                                d="M15 14s1 0 1-1-1-4-5-4-5 3-5 4 1 1 1 1zm-7.978-1A.261.261 0 0 1 7 12.996c.001-.264.167-1.03.76-1.72C8.312 10.629 9.282 10 11 10c1.717 0 2.687.63 3.24 1.276.593.69.758 1.457.76 1.72l-.008.002a.274.274 0 0 1-.014.002H7.022ZM11 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4m3-2a3 3 0 1 1-6 0 3 3 0 0 1 6 0M6.936 9.28a5.88 5.88 0 0 0-1.23-.247A7.35 7.35 0 0 0 5 9c-4 0-5 3-5 4 0 .667.333 1 1 1h4.216A2.238 2.238 0 0 1 5 13c0-1.01.377-2.042 1.09-2.904.243-.294.526-.569.846-.816M4.92 10A5.493 5.493 0 0 0 4 13H1c0-.26.164-1.03.76-1.724.545-.636 1.492-1.256 3.16-1.275ZM1.5 5.5a3 3 0 1 1 6 0 3 3 0 0 1-6 0m3-2a2 2 0 1 0 0 4 2 2 0 0 0 0-4" />
                                        </svg>
                                    </div>
                                </a>
                                <div class="deposit-name">
                                    <p>Team Info</p>
                                </div>
                            </div>


                            <div class="deposit d-flex flex-column align-items-center col-4">
                                <a href="support.php">
                                    <div class="icons">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-info-circle" viewBox="0 0 16 16">
                                            <!-- <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" /> -->
                                            <!-- <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z" /> -->

                                            <path
                                                d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5">
                                            </path>
                                        </svg>
                                    </div>
                                </a>
                                <div class="deposit-name">
                                    <!-- <p>Support</p> -->
                                    <p>Feedback</p>
                                </div>
                            </div>
                            <div class="deposit d-flex flex-column align-items-center col-4">
                                <a href="video.php">
                                    <div class="icons4">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-camera-video" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd"
                                                d="M0 5a2 2 0 0 1 2-2h7.5a2 2 0 0 1 1.983 1.738l3.11-1.382A1 1 0 0 1 16 4.269v7.462a1 1 0 0 1-1.406.913l-3.111-1.382A2 2 0 0 1 9.5 13H2a2 2 0 0 1-2-2zm11.5 5.175 3.5 1.556V4.269l-3.5 1.556zM2 4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h7.5a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1z" />
                                        </svg>
                                    </div>
                                </a>
                                <div class="deposit-name">
                                    <!-- <p>Support</p> -->
                                    <p>Tutorial Videos</p>
                                </div>
                            </div>


                        </div>
                    </div>



                    <div class="apk mt-1">
                        <button class="download redownload" onclick="window.location.href = 'ATB_1_1.0.apk'">Download an APK</button>
                    </div>
                </div>
            </div>
        </div>

    </div>


    <script>
        function copyLink() {
            /* Get the text field */
            var copyText = document.getElementById("invite-link");

            /* Select the text field */
            copyText.select();
            copyText.setSelectionRange(0, 99999); /* For mobile devices */

            /* Copy the text inside the text field */
            document.execCommand("copy");

            /* Alert the copied text */
            //   alert("Copied the link: " + copyText.value);
        }
    </script>

    <script src="assets/nav.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
        crossorigin="anonymous"></script>
</body>

</html>