<?php
session_start();
include('../admin/lib/db_connection.php');
$page = 0;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
$date_time = date('Y-m-d H:i:s');

if ($_REQUEST['captcha'] != $_SESSION['captcha']) {
	echo 3;
	die();
} else {


	$sel = dbQuery("SELECT * FROM tabl_user WHERE (sponsor_id='" . $_REQUEST['username'] . "' OR phone='" . $_REQUEST['username'] . "') AND password='" . md5($_REQUEST['password']) . "' AND status=1");
	$num = dbNumRows($sel);
	$res = dbFetchAssoc($sel);
	if ($num > 0) {

		$_SESSION['user_id'] = $res['id'];
		$_SESSION['sponsor_id'] = $res['sponsor_id'];
		echo 1;
	} else {
		echo 2;
	}
}
