<?php
session_start();
error_reporting(0);
include('admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

// Recursive function to calculate total investment for a user and its downlines up to 10 levels.
function getInvestmentForUser($userId, $level = 1, $maxLevel = 10)
{
    if ($level > $maxLevel) {
        return 0;
    }

    // Get the user's own investment from tabl_plan_transaction.
    $sql = "SELECT SUM(price) as total FROM tabl_plan_transaction WHERE user_id = '$userId'";
    $result = dbQuery($sql);
    $row = dbFetchAssoc($result);
    $investment = isset($row['total']) ? $row['total'] : 0;

    // Get the direct downlines of the user from tabl_user.
    $sql2 = "SELECT id FROM tabl_user WHERE upliner_id = '$userId'";
    $result2 = dbQuery($sql2);
    while ($child = dbFetchAssoc($result2)) {
        // Recursively add investments from each child (and their subsequent downlines).
        $investment += getInvestmentForUser($child['id'], $level + 1, $maxLevel);
    }

    return $investment;
}

// Main function to calculate leadership income based on the two hands.
function calculateLeadershipInvestment($topUserId)
{
    // Retrieve direct downlines (hands) of the top user.
    $sql = "SELECT id FROM tabl_user WHERE upliner_id = '$topUserId'";
    $result = dbQuery($sql);

    $hands = array(); // Array to store investment of each direct downline branch.
    while ($direct = dbFetchAssoc($result)) {
        // For each direct downline, calculate total investment including its entire branch (up to 10 levels).
        $investment = getInvestmentForUser($direct['id'], 1, 10);
        $hands[] = $investment;
    }

    // If there are no direct downlines, return zeros.
    if (empty($hands)) {
        return array('first_hand' => 0, 'second_hand' => 0, 'grand_total' => 0);
    }

    // Identify the hand with the maximum investment.
    $maxInvestment = max($hands);
    // Calculate the total investment across all hands.
    $grandTotal = array_sum($hands);
    // The second hand is the total investment from all other downline branches.
    $secondHand = $grandTotal - $maxInvestment;

    // Return the results.
    return array(
        'first_hand'  => $maxInvestment,
        'second_hand' => $secondHand,
        'grand_total' => $grandTotal
    );
}

// Example usage:
// $topUserId = 1; // Replace with the actual top user id
// $result = calculateLeadershipInvestment($topUserId);
// echo "First Hand Total: " . $result['first_hand'] . " USD<br>";
// echo "Second Hand Total: " . $result['second_hand'] . " USD<br>";
// echo "Grand Total: " . $result['grand_total'] . " USD<br>";



// Main daily income processing loop.
$sel = dbQuery("SELECT tabl_user.*, tabl_plans.id as plan_id, tabl_plans.income as daily_income 
                FROM tabl_user 
                INNER JOIN tabl_plans ON tabl_user.membership_id = tabl_plans.id 
                WHERE tabl_user.membership_id != 0");

while ($res = dbFetchAssoc($sel)) {
    $user_id = $res['id'];
    $plan_id = $res['plan_id'];
    $today = date('Y-m-d');

    echo "User: " . $user_id . " - Plan: " . $plan_id . "<br>";

    // Retrieve plan details
    $sel_plan = dbQuery("SELECT * FROM tabl_plans WHERE id='$plan_id'");
    $sel_plan_transaction = dbQuery("SELECT * FROM tabl_plan_transaction WHERE plan_id='$plan_id' AND status='1' ORDER BY id DESC LIMIT 1");
    $res_plan_transaction = dbFetchAssoc($sel_plan_transaction);
    $res_plan = dbFetchAssoc($sel_plan);

    if ($res_plan && $res_plan_transaction) {
        $price = $res_plan_transaction['price'];
        $income_per = $res_plan['income'];

        // Calculate daily income
        $daily_income = $price * $income_per / 100;

        // Insert daily income
        dbQuery("INSERT INTO tabl_main_wallet SET user_id='$user_id', receive_amount='$daily_income', type=1, type_id=3, narration='Daily Income', date_added='$today'");

        // Calculate level income for upliners
        if (!empty($res['upliner_id'])) {
            calculateLevelIncome($res['upliner_id'], $user_id, $daily_income, 1, $today);
        }

        // Check if Leadership Income was already given today
        $checkLeadership = dbQuery("SELECT id FROM tabl_leadership_income_records WHERE user_id='$user_id' AND date_added='$today' LIMIT 1");
        if (dbFetchAssoc($checkLeadership)) {
            echo "User $user_id has already received Leadership Income today.<br>";
            continue; // Skip this user for leadership income
        }

        // Calculate Leadership Investment
        $leadershipInvestment = calculateLeadershipInvestment($user_id);
        $firstHand = $leadershipInvestment['first_hand'];
        $secondHand = $leadershipInvestment['second_hand'];
        $totalInvestment = $leadershipInvestment['grand_total'];

        // Ensure 1st hand is greater than 60% of total investment
        if ($totalInvestment > 0 && $firstHand >= ($totalInvestment * 0.6)) {
            echo "User $user_id qualifies for Leadership Income!<br>";

            // Get only ONE plan with the highest possible investment the user qualifies for
            $leadershipPlan = dbQuery("SELECT id, investment, reward, duration 
                                       FROM tabl_leadership_income 
                                       WHERE investment <= '$totalInvestment' 
                                       ORDER BY investment DESC 
                                       LIMIT 1");

            if ($plan = dbFetchAssoc($leadershipPlan)) {
                $leadershipIncomeId = $plan['id'];
                $rewardAmount = $plan['reward'];
                $durationMonths = $plan['duration'];

                // Insert Leadership Income once for today
                dbQuery("INSERT INTO tabl_main_wallet SET 
                    user_id='$user_id', 
                    receive_amount='$rewardAmount', 
                    type=2, 
                    type_id=4, 
                    narration='Leadership Income', 
                    date_added='$today'");

                // Store record in leadership income tracking table
                dbQuery("INSERT INTO tabl_leadership_income_records SET 
                    leadership_income_id='$leadershipIncomeId',
                    user_id='$user_id',
                    first_hand='$firstHand',
                    second_hand='$secondHand',
                    total_investment='$totalInvestment',
                    reward_amount='$rewardAmount',
                    duration='$durationMonths',
                    date_added='$today'
                ");

                echo "Leadership income of $rewardAmount USD added for User $user_id.<br>";
            }
        }
    }
}
