<?php



// if ($m_wallet < $res['earning_limit']) {
// if ($m_wallet < $res['earning_limit']) {

$sel_plan_req = dbQuery("SELECT * FROM tabl_plan_transaction WHERE user_id='" . $_SESSION['user_id'] . "' AND is_upgrade='1' AND status='0' ORDER BY id DESC");

if (!$res_plan_req = dbFetchAssoc($sel_plan_req)) {

    $exist_plan_price = $res_exist_plan['price'];
    $total_balance = $m_wallet + $res_exist_plan['price'];

    $plan = dbQuery("SELECT * FROM tabl_plans WHERE price>'$exist_plan_price' AND price<='$total_balance'  ORDER BY id DESC LIMIT 1");
    if ($res_plan = dbFetchAssoc($plan)) {

        $plan_id = $res_plan['id'];

        $deposite_charges = $res_plan['price'] * 6 / 100;
        // $new_amount = (float) $res_plan['price'] + (float) $deposite_charges;
        $new_amount = (float) $res_plan['price'];
        if ($m_wallet > $new_amount) {

            // echo '<option value="' . $res_plan['id'] . '">' . $res_plan['rank'] . ' -- ' . round($new_amount, 2) . '</option>';

            $total_price = $new_amount - $exist_plan_price;

            $result = dbQuery("INSERT INTO tabl_plan_transaction SET user_id='" . $_SESSION['user_id'] . "',plan_id='" . $res_plan['id'] . "',price='" . $new_amount . "',reference_id='', is_upgrade='1', status='1', screen_shot='',date_added='" . $date . "'");

            if ($result) {
                $earning_limit = (float) $res_plan['price'] * 3;

                dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $_SESSION['user_id'] . "', receive_amount='0', paid_amount='" . $total_price . "', type='2', type_id='7', narration='Upgrade Plan', date_added='" . $date . "'");

                dbQuery("UPDATE tabl_user SET earning_limit=earning_limit+'" . $earning_limit . "',membership_id='" . $plan_id . "' WHERE id='" . $_SESSION['user_id'] . "'");


                echo "<script>alert('Plan Upgraded!');window.location.href = 'index.php';</script>";
            }
            // else {
            //     echo "<script>alert('Plan Upgrade Request failed! We will authorize the transaction and apply the plan as you requested!');window.location.href = 'dashboard.php';</script>";
            // }
        }
    }

}



// $button = '<button type="submit" id="submit" name="submit" class="btn btn1 btn-primary w-100">Upgrade Plan</button>';


// if (isset($_REQUEST['submit'])) {
//     $sel = dbQuery("SELECT * FROM tabl_plans WHERE id='" . $_REQUEST['plan_id'] . "'");
//     $res = dbFetchAssoc($sel);

//     $result = dbQuery("INSERT INTO tabl_plan_transaction SET user_id='" . $_SESSION['user_id'] . "',plan_id='" . $_REQUEST['plan_id'] . "',price='" . $res['price'] . "',reference_id='', is_upgrade='1', screenshot='',date_added='" . $date . "'");

//     if ($result) {
//         echo "<script>alert('Plan Upgrade Request addded! We will authorize the transaction and apply the plan as you requested!');window.location.href = 'dashboard.php';</script>";
//     } else {
//         echo "<script>alert('Plan Upgrade Request failed! We will authorize the transaction and apply the plan as you requested!');window.location.href = 'dashboard.php';</script>";
//     }
// }
