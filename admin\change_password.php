<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 4;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

if (isset($_REQUEST['submit'])) {

  $old_password = md5(mysqli_real_escape_string($con, $_REQUEST['old_password']));

  $password = $_REQUEST['password'];
  $confirm_password = $_REQUEST['confirm_password'];


  $admin_q = dbQuery("SELECT * FROM tabl_admin WHERE id='" . $_SESSION['admin_id'] . "'");
  $admin = dbFetchAssoc($admin_q);
  if ($old_password == $admin['password']) {

    if ($password == $confirm_password) {
      $result = dbQuery("UPDATE tabl_admin SET `password`='" . mysqli_real_escape_string($con, md5($_REQUEST['password'])) . "' WHERE id='" . $_SESSION['admin_id'] . "'");
      if ($password == $confirm_password) {
        echo '<script>alert("Password changed successfully!");window.location.href="change_password.php"</script>';
      } else {
        echo '<script>alert("Something went wrong!");window.location.href="change_password.php"</script>';
      }
    } else {
      echo '<script>alert("Both password not match!");window.location.href="change_password.php"</script>';
    }
  } else {
    echo '<script>alert("Wrong password entered!");window.location.href="change_password.php"</script>';
  }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
  <title><?php echo SITE; ?>- Change Password</title>
  <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
  <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
  <script src="assets/js/loader.js"></script>

  <!-- BEGIN GLOBAL MANDATORY STYLES -->
  <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
  <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
  <!-- END GLOBAL MANDATORY STYLES -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
  <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
  <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
  <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

  <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
  <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">

  <style>
    .eye_icon {
      border-radius: 0px 5px 5px 0px !important;
    }
  </style>
</head>

<body class="alt-menu sidebar-noneoverflow">
  <?php include('inc/__header.php');

  //print_r($_SESSION);


  ?>
  <!--  BEGIN MAIN CONTAINER  -->
  <div class="main-container" id="container">
    <div class="overlay"></div>
    <div class="search-overlay"></div>

    <!--  BEGIN TOPBAR  -->
    <?php include('inc/__menu.php'); ?>
    <!--  END TOPBAR  -->

    <!--  BEGIN CONTENT PART  -->
    <div id="content" class="main-content">
      <div class="layout-px-spacing">
        <nav class="breadcrumb-one" aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">Home</a></li>
            <li class="breadcrumb-item active"><a href="javascript:void(0);">Change Password</a></li>
          </ol>
        </nav>
        <div class="account-settings-container layout-top-spacing">
          <div class="account-content">
            <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
              <div class="row">
                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                  <form name="account" method="post" id="general-info" class="section general-info" enctype="multipart/form-data">
                    <div class="info">
                      <h6 class="">Change Password</h6>
                      <div class="row">
                        <div class="col-lg-11 mx-auto">
                          <div class="row">
                            <div class="col-xl-12 col-lg-12 col-md-8 mt-md-0 ">
                              <div class="widget-content widget-content-area">
                                <div class="form-row mb-6">
                                  <div class="form-group col-md-4">
                                    <label for="old_password"><strong>Old Password</strong></label>

                                    <div class="input-group mb-3">
                                      <!-- <input type="password" class="form-control" id="password" name="password" placeholder="Please enter password"> -->
                                      <input type="password" name="old_password" id="old_password" class="form-control" placeholder="Enter Old Password" required>
                                      <div class="input-group-prepend">
                                        <span class="input-group-text eye_icon pointer" onclick="show_pass('old_password');">
                                          <i class="fa fa-eye" aria-hidden="true"></i>
                                        </span>
                                      </div>
                                    </div>

                                  </div>
                                  <div class="form-group col-md-4">
                                    <label for="password"><strong>Password</strong></label>

                                    <div class="input-group mb-3">
                                      <input type="password" class="form-control" id="password" name="password" placeholder="Please enter password" required>
                                      <div class="input-group-prepend">
                                        <span class="input-group-text eye_icon pointer" onclick="show_pass('password');">
                                          <i class="fa fa-eye" aria-hidden="true"></i>
                                        </span>
                                      </div>
                                    </div>

                                    <!-- <input type="password" name="password" id="password" class="form-control" placeholder="Enter Password" required> -->
                                  </div>

                                  <div class="form-group col-md-4">
                                    <label for="inputEmail4"><strong>Confirm Password</strong></label>

                                    <div class="input-group mb-3">
                                      <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Please enter password" required>
                                      <div class="input-group-prepend">
                                        <span class="input-group-text eye_icon pointer" onclick="show_pass('confirm_password');">
                                          <i class="fa fa-eye" aria-hidden="true"></i>
                                        </span>
                                      </div>
                                    </div>

                                    <!-- <input type="password" name="confirm_password" id="confirm_password" class="form-control" placeholder="Enter Confirm Password" required> -->
                                  </div>
                                </div>
                                <div class="form-row">
                                  <button type="submit" name="submit" class="btn btn-primary ">Submit</button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
        <?php include('inc/__footer.php'); ?>
      </div>
    </div>
    <!--  END CONTENT PART  -->

  </div>
  <!-- END MAIN CONTAINER -->

  <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
  <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
  <script src="bootstrap/js/popper.min.js"></script>
  <script src="bootstrap/js/bootstrap.min.js"></script>
  <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
  <script src="assets/js/app.js"></script>
  <script>
    $(document).ready(function() {
      App.init();
    });

    function show_pass(inputId) {
      var inputElement = document.getElementById(inputId);
      var eyeIcon = inputElement.parentElement.querySelector('.eye_icon i');

      if (inputElement.type === "password") {
        inputElement.type = "text";
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
      } else {
        inputElement.type = "password";
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
      }
    }
  </script>
  <script src="assets/js/custom.js"></script>
  <!-- END GLOBAL MANDATORY SCRIPTS -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
  <script src="plugins/apex/apexcharts.min.js"></script>
  <script src="assets/js/dashboard/dash_2.js"></script>
  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

  <script src="plugins/dropify/dropify.min.js"></script>
  <script src="plugins/blockui/jquery.blockUI.min.js"></script>
  <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
  <script src="assets/js/users/account-settings.js"></script>

  <script>
    var password = document.getElementById("password"),
      confirm_password = document.getElementById("confirm_password");

    function validatePassword() {
      if (password.value != confirm_password.value) {
        confirm_password.setCustomValidity("Passwords Don't Match");
      } else {
        confirm_password.setCustomValidity('');
      }
    }
    password.onchange = validatePassword;
    confirm_password.onkeyup = validatePassword;
  </script>
</body>

</html>