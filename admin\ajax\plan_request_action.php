<?php
session_start();
include('../lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

// Recursive function for calculating referral income
function calculateReferralIncome($upliner_id, $user_id, $price, $level, $date)
{
	// Get referral percentage for the current level
	$sel_income = dbQuery("SELECT referral_income FROM tabl_referral_income WHERE `level`='" . $level . "'");
	$res_income = dbFetchAssoc($sel_income);

	if (!$res_income) {
		return; // Stop if no percentage is found for this level
	}

	$percentage = $res_income['referral_income'];
	$refer_bonus = $price * $percentage / 100;

	// Get upliner information
	$sel_user = dbQuery("SELECT upliner_id, earning_limit FROM tabl_user WHERE id='" . $upliner_id . "'");
	$res_user = dbFetchAssoc($sel_user);

	if (!$res_user) {
		return; // Stop if no upliner is found
	}

	// Check earning limit for upliner
	$refer_wallet = dbQuery("SELECT SUM(receive_amount) as total_earning FROM tabl_main_wallet WHERE user_id='" . $upliner_id . "'");
	$res_wallet = dbFetchAssoc($refer_wallet);

	if (($res_wallet['total_earning'] + $refer_bonus)  < $res_user['earning_limit']) {
		// Add referral bonus to wallet
		dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $upliner_id . "', receive_amount='" . $refer_bonus . "', type=1, type_id=2, to_id='" . $user_id . "', narration='Referral Bonus Level $level', date_added='" . $date . "'");
	} else {
		return; // Stop if earning limit is reached
	}

	// Recursive call for the next level
	calculateReferralIncome($res_user['upliner_id'], $user_id, $price, $level + 1, $date);
}



if ($_REQUEST['val'] == 1) {
	$sel = dbQuery("SELECT * FROM tabl_plan_transaction WHERE id='" . $_REQUEST['d_id'] . "'");
	$res = dbFetchAssoc($sel);

	$user_id = $res['user_id'];
	$plan_id = $res['plan_id'];
	$price = $res['price'];

	// $earning_limit = $price * 3;
	$earning_limit = $price * 2.5;
	// $self_bonus = $price * 10 / 100;
	// $self_bonus = $price * 14 / 100;

	//#####################Credit Self Bonus

	// dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $user_id . "',receive_amount='" . $self_bonus . "',type=1,type_id=1,narration='Self Bonus',date_added='" . $date . "'"); // comment due to off self bonus
	//#################### END Self Bonus

	//Refer Income

	//FIRST
	$sel_user = dbQuery("SELECT upliner_id FROM tabl_user WHERE id='" . $user_id . "'");
	$res_user = dbFetchAssoc($sel_user);


	// Start calculating referral income from level 1
	calculateReferralIncome($res_user['upliner_id'], $user_id, $price, 1, $date);


	// $refer1 = dbQuery("SELECT id, upliner_id, earning_limit FROM tabl_user WHERE id='" . $res_user['upliner_id'] . "'");
	//End REFER INCOME Here


	//Start 

	// $quantum = dbQuery("SELECT * FROM tabl_user WHERE upliner_id='" . $res_user['upliner_id'] . "'");
	// $new_num_quantum = dbNumRows($quantum);
	// $res_quantum = dbFetchAssoc($quantum);

	// $refer_wallet_info = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_user['upliner_id'] . "'");
	// $res_refer_wallet_info = dbFetchAssoc($refer_wallet_info);
	// if ($res_refer_wallet_info['total_earning'] < $res_quantum['earning_limit']) {

	// 	if ($new_num_quantum >= 10 && $new_num_quantum < 30) {
	// 		$reward = 50;
	// 		dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_user['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 10 Direct',date_added='" . $date . "'");
	// 	} elseif ($new_num_quantum >= 30 && $new_num_quantum < 50) {
	// 		$reward = 100;
	// 		dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_user['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 30 Direct',date_added='" . $date . "'");
	// 	} elseif ($new_num_quantum >= 50 && $new_num_quantum < 70) {

	// 		$reward = 150;
	// 		dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_user['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 50 Direct',date_added='" . $date . "'");
	// 	} elseif ($new_num_quantum >= 70 && $new_num_quantum < 100) {

	// 		$reward = 200;
	// 		dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_user['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 70 Direct',date_added='" . $date . "'");
	// 	} elseif ($new_num_quantum >= 100) {

	// 		$reward = 500;
	// 		dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_user['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 100 Direct',date_added='" . $date . "'");
	// 	}
	// }

	//End Quantum Reward	

	dbQuery("UPDATE tabl_user SET earning_limit='" . $earning_limit . "',membership_id='" . $plan_id . "' WHERE id='" . $user_id . "'");
	dbQuery("UPDATE tabl_plan_transaction SET status='" . $_REQUEST['val'] . "' WHERE id='" . $_REQUEST['d_id'] . "'");
	echo 1;
} else {
	dbQuery("UPDATE tabl_plan_transaction SET status='" . $_REQUEST['val'] . "' WHERE id='" . $_REQUEST['d_id'] . "'");
	echo 2;
}
