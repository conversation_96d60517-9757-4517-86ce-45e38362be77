<?php 
session_start();
include('../lib/db_connection.php');
if(!empty($_POST["keyword"])){	
$sel ="select * from tabl_user where sponsor_id LIKE '".$_POST["keyword"]."%' ORDER BY sponsor_id ASC";
$query = dbQuery($sel);
?>
<ul id="search-list">
<?php
while($result=dbFetchAssoc($query))
{?>
<li onClick="click01('<?php echo $result["id"];?>,<?php echo $result["sponsor_id"].' - '. $result["name"];?>');"><?php echo $result["sponsor_id"].' - '. $result["name"]; ?></li>
<?php } ?>
</ul>
<?php } ?>
