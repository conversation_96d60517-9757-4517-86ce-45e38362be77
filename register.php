<?php

if (isset($_REQUEST['ref']) && $_REQUEST['ref'] != "") {
    $ref = $_REQUEST['ref'];
} else {
    $ref = "";
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Login Page</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        body {
            background-color: #e5dbed;

        }


        .login-container {
            max-width: 300px;
            margin: 0 auto;
            padding: 20px;
            margin-top: 50px;
            margin-bottom: 50px;
            box-shadow: 0 0 10px rgba(156, 24, 176, 0.1);
            background-color: #ffffff;
            border-radius: 8px;
        }

        .login-container h2 {

            color: #823fb5;
            margin-bottom: 30px;
            text-align: center;
            font-size: 22px;
            font-weight: bold;
        }

        .login-container form {
            margin-bottom: 15px;
        }

        .login-container .btn-primary {
            background-color: #823fb5;
            border-color: #823fb5;
            width: 100%;
        }

        .login-container .btn-primary:hover {
            background: none;
            border: 1px solid #823fb5;
            color: #823fb5;

        }


        .forgot-password,
        .register-link {
            text-align: center;
            margin-top: 15px;
        }

        .forgot-password a,
        .register-link a {
            color: #823fb5;
        }

        .forgot-password a:hover,
        .register-link a:hover {
            text-decoration: underline;
        }

        .form-control {
            border-radius: 20px;
        }

        .login-container .btn-primary {
            background-color: #823fb5;
            border-color: #823fb5;
            width: 100%;
            border-radius: 20px;
        }

        .image {
            text-align: center;
            background-color: #823fb5;
        }

        .image img {
            width: 30%;
        }

        .required::before {
            content: "*";
            color: red;
            margin: 5px;
        }


        .eye_icon {
            border-radius: 0px 20px 20px 0px !important;
        }

        .pointer {
            cursor: pointer;
        }

        .custom-select {
            width: 10% !important;
            border-radius: 20px 0px 0px 20px !important;
        }

        #mobile_code {
            width: 60%
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- <div class="circle">
            <img src="assets/image/logo.jpeg" alt="Example Image">
        </div> -->

        <div class="login-container">
            <!-- <div class="image">
           <img src="assets/image/logo-removebg-preview (1).png" alt="">
        </div> -->

            <form id="signform" class="mb-3" method="POST">
                <h2>Register</h2>
                <div class="form-group">
                    <label class="required" for="code">Invitation Code</label>
                    <input type="text" class="form-control" id="ref_code" name="ref_code" placeholder="Please enter 6-12 letters" value="<?php echo $ref; ?>" onkeypress="preventSpace(event)">
                </div>
                <div class="form-group">
                    <label class="required" for="mobileNumber">Username</label>
                    <input type="text" class="form-control" id="username" name="username" placeholder="Please enter username" onchange="check_username(this.value);" onkeypress="preventSpace(event)">
                    <div class="username_loader"></div>
                </div>
                <div class="form-group">
                    <label class="required" for="email">Email</label>
                    <input type="email" class="form-control" id="email" name="email" placeholder="Please enter your email">
                </div>

                <div class="form-group">
                    <label class="required" for="password">Phone Number</label>

                    <div class="input-group mb-3">

                        <select class="custom-select" id="country_code" name="country_code">
                            <option value="+1">+1 (USA)</option>
                            <option value="+44">+44 (UK)</option>
                            <option value="+91" selected>+91 (India)</option>
                            <option value="+86">+86 (China)</option>
                            <option value="+81">+81 (Japan)</option>
                            <option value="+7">+7 (Russia)</option>
                            <option value="+49">+49 (Germany)</option>
                            <option value="+33">+33 (France)</option>
                            <option value="+61">+61 (Australia)</option>
                            <option value="+971">+971 (UAE)</option>
                            <option value="+55">+55 (Brazil)</option>
                            <option value="+92">+92 (Pakistan)</option>
                            <option value="+39">+39 (Italy)</option>
                            <option value="+81">+81 (Japan)</option>
                            <option value="+82">+82 (South Korea)</option>
                            <option value="+34">+34 (Spain)</option>
                            <option value="+52">+52 (Mexico)</option>
                            <option value="+65">+65 (Singapore)</option>
                            <option value="+60">+60 (Malaysia)</option>
                            <option value="+971">+971 (UAE)</option>
                        </select>
                        <input type="number" class="form-control" id="mobile_code" name="phone" placeholder="Please enter your phone">
                    </div>

                </div>

                <div class="form-group">
                    <label class="required" for="password">Login Password</label>
                    <div class="input-group mb-3">
                        <input type="password" class="form-control" id="password" name="password" placeholder="Please enter password">
                        <div class="input-group-prepend">
                            <span class="input-group-text eye_icon pointer" onclick="show_pass('password');">
                                <i class="fa fa-eye" aria-hidden="true"></i>
                            </span>
                        </div>
                    </div>

                </div>

                <div class="form-group">
                    <label class="required" for="confirm_password">Confirm Password</label>
                    <div class="input-group mb-3">
                        <input type="password" class="form-control" id="confirm_password" name="con_password" placeholder="Please enter password">
                        <div class="input-group-prepend">
                            <span class="input-group-text eye_icon pointer" onclick="show_pass('confirm_password');">
                                <i class="fa fa-eye" aria-hidden="true"></i>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="required" for="fund_password">Withdrawal Password</label>
                    <div class="input-group mb-3">
                        <input type="password" class="form-control" id="fund_password" name="fund_password" placeholder="Please enter the password">
                        <div class="input-group-prepend">
                            <span class="input-group-text eye_icon pointer" onclick="show_pass('fund_password');">
                                <i class="fa fa-eye" aria-hidden="true"></i>
                            </span>
                        </div>
                    </div>
                </div>


                <input type="hidden" name="action" value="submit" required>
                <button type="submit" class="btn btn-primary" id="register">Register now</button>

                <div class="loader_login mt-2"></div>
            </form>

            <form id="otpform" class="mb-3" method="POST" style="display: none">
                <h2>Register</h2>
                <div class="form-group">
                    <label class="required" for="otp">Enter OTP</label>
                    <input type="password" class="form-control" id="otp" name="otp" placeholder="Enter OTP" autofocus>
                </div>

                <input type="hidden" name="action" value="submit" required>

                <button type="submit" class="btn btn-primary">Verify OTP</button>

                <!-- <button class="btn btn-primary d-grid w-100">Verify OTP</button> -->
                <div class="loader_otp mt-2"></div>
            </form>

            <div class="register-link">
                <p>Already have an account? <a href="login.php">Login here</a></p>
            </div>
        </div>
    </div>
    <script>
        // -----Country Code Selection
        $("#mobile_code").intlTelInput({
            initialCountry: "in",
            separateDialCode: true,
            // utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/11.0.4/js/utils.js"
        });
    </script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.0.7/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>


    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>



    <script>
        $("#signform").submit(function(e) {

            // alert('hello');

            $("#submit").attr('disabled', true);
            $(".loader_login").html(' please wait...');
            $.ajax({
                url: 'ajax/user_registration.php',
                type: 'post',
                data: $("#signform").serialize(),
                success: function(data) {
                    var arr = data.split('~');
                    if (arr[0] == 1) {
                        // $(".loader_login").html('<div class="alert alert-success text-center" role="alert">OTP Sent on Your Email ID!</div>');
                        // $("#submit").attr('disabled', true);
                        // $("#otpform").css('display', 'block');
                        // $("#signform").css('display', 'none');

                        $(".loader_login").html('<div class="alert alert-success text-center" role="alert">You Are Registered Successfuly!<br> <a href="login.php">Click Here</a> to login</div>');
                        //$("#otpform").css('display', 'none');
                        $('#signform').trigger("reset");

                        // $(location).attr('href', './logout.php');

                        register_popup(arr[1]);


                    } else if (arr[0] == 2) {
                        $("#submit").attr('disabled', false);
                        $(".loader_login").html('<div class="alert alert-danger text-center" role="alert">Email Address OR Phone no is already Exist!</div>');
                        setTimeout(function() {
                            $(".loader_login").html('');
                        }, 8000);

                    } else if (arr[0] == 3) {
                        $("#submit").attr('disabled', false);
                        $(".loader_login").html('<div class="alert alert-danger text-center" role="alert">Both Passwords are not match!</div>');
                        setTimeout(function() {
                            $(".loader_login").html('');
                        }, 8000);

                    } else if (arr[0] == 4) {
                        $("#submit").attr('disabled', false);
                        $(".loader_login").html('<div class="alert alert-danger text-center" role="alert">Please agree with our terms & conditions first!</div>');
                        setTimeout(function() {
                            $(".loader_login").html('');
                        }, 8000);

                    } else if (arr[0] == 5) {
                        $("#submit").attr('disabled', false);
                        $(".loader_login").html('<div class="alert alert-danger text-center" role="alert">Invalid Referral Code!</div>');
                        setTimeout(function() {
                            $(".loader_login").html('');
                        }, 8000);
                    } else if (arr[0] == 6) {
                        $("#submit").attr('disabled', false);
                        $(".loader_login").html('<div class="alert alert-danger text-center" role="alert">Invalid Referral Code!</div>');
                        setTimeout(function() {
                            $(".loader_login").html('');
                        }, 8000);

                    } else {
                        $("#submit").attr('disabled', false);
                        $(".loader_login").html('<div class="alert alert-danger text-center" role="alert">Something Went Wrong!</div>');
                        setTimeout(function() {
                            $(".loader_login").html('');
                        }, 8000);
                    }
                },
            });
            e.preventDefault(); // avoid to execute the actual submit of the form.
        });
    </script>


    <script>
        $("#otpform").submit(function(e) {
            $("#submit").attr('disabled', true);
            $(".loader_otp").html(' please wait...');
            $.ajax({
                url: 'ajax/verify_otp.php',
                type: 'post',
                data: $("#otpform").serialize(),
                success: function(data) {
                    var arr = data.split('~');
                    if (arr[0] == 1) {
                        $(".loader_otp").html('<div class="alert alert-success text-center" role="alert">You Are Registered Successfuly!<br> <a href="login.php">Click Here</a> to login</div>');
                        //$("#otpform").css('display', 'none');
                        $('#signform').trigger("reset");

                        // $(location).attr('href', './logout.php');

                        register_popup(arr[1]);

                    } else if (arr[0] == 2) {
                        $("#submit").attr('disabled', false);
                        $(".loader_otp").html('<div class="alert alert-danger text-center" role="alert">Somthing Went Wrong!</div>');
                        setTimeout(function() {
                            $(".loader_otp").html('');
                        }, 8000);
                    } else {
                        $("#submit").attr('disabled', false);
                        $(".loader_otp").html('<div class="alert alert-danger text-center" role="alert">Wrong OTP Entered!</div>');
                        setTimeout(function() {
                            $(".loader_otp").html('');
                        }, 8000);
                    }
                },
            });
            e.preventDefault(); // avoid to execute the actual submit of the form.
        });



        function register_popup(user_id) {
            $.ajax({
                url: 'ajax/register_popup.php',
                type: 'post',
                data: {
                    'user_id': user_id
                },
                success: function(data) {
                    $("#register_popup").html(data);
                    $(location).attr('href', '#register_info');

                    // if (data == 1) {
                    //   $("#register_popup").html('');
                    //   $(location).attr('href', '#register_info');
                    // } else {
                    //   $(".loader_otp").html('This email is Already Exist!');
                    //   $(location).attr('href', './logout.php');
                    // }
                },
            });
        }
    </script>

    <script>
        function check_email(email) {
            $(".email_loader").html('please wait...');
            $.ajax({
                url: 'ajax/check_email.php',
                type: 'post',
                data: {
                    'email': email
                },
                success: function(data) {
                    if (data == 1) {
                        $(".email_loader").html('');
                    } else {
                        $(".email_loader").html('This email is Already Exist!');
                    }
                },
            });
        }


        function check_username(username) {
            $(".username_loader").html('please wait...');
            $.ajax({
                url: 'ajax/check_username.php',
                type: 'post',
                data: {
                    'username': username
                },
                success: function(data) {
                    if (data == 1) {
                        $(".username_loader").html('');
                        $("#register").removeClass('disabled');
                        // $(".register").('disabled');
                        // Disable the button with ID "myButton"
                        $("#register").prop("disabled", false);
                        
                    } else {
                        $(".username_loader").html('This username is already exist!');
                        $("#register").addClass('disabled');
                        $("#register").prop("disabled", true);
                    }
                },
            });
        }


        function preventSpace(event) {
            // Get the key code of the pressed key
            var keyCode = event.keyCode || event.which;

            // Check if the pressed key is a space (keyCode 32)
            if (keyCode === 32) {
                // Prevent the default action (i.e., entering the space)
                event.preventDefault();
            }
        }
    </script>

    <script>
        function check_phone(phone) {
            $(".phone_loader").html('please wait...');
            $.ajax({
                url: 'ajax/check_phone.php',
                type: 'post',
                data: {
                    'phone': phone
                },
                success: function(data) {
                    if (data == 1) {
                        $(".phone_loader").html('');
                    } else {
                        $(".phone_loader").html('This Contact no is Already Exist!');
                    }
                },
            });
        }

        function isNumberKey(evt) {
            var charCode = (evt.which) ? evt.which : evt.keyCode
            if (charCode > 31 && (charCode < 48 || charCode > 57))
                return false;
            return true;
        }
    </script>





    <!--  -->

    <script>
        $("#login_form").submit(function(e) {
            $("#submit").attr('disabled', true);
            $("#submit").html('<i class="spinner-border align-self-center" style="width="20" height="20"></i> processing...');
            $.ajax({
                url: 'ajax/signup.php',
                type: 'post',
                data: $("#login_form").serialize(),
                success: function(data) {
                    if (data == 1) {
                        $("#submit").attr('disabled', false);
                        $("#submit").html('REGISTER');
                        $(".msg").html('<span style="color:green">Registration successfully!</span>');
                        setTimeout(function() {
                            $(".msg").html('');
                        }, 8000);
                    } else if (data == 2) {
                        $("#submit").attr('disabled', false);
                        $("#submit").html('REGISTER');
                        $(".msg").html('<span style="color:red">Username not available!</span>');
                        setTimeout(function() {
                            $(".msg").html('');
                        }, 8000);
                    } else if (data == 3) {
                        $("#submit").attr('disabled', false);
                        $("#submit").html('REGISTER');
                        $(".msg").html('<span style="color:red">Phone no. not available!</span>');
                        setTimeout(function() {
                            $(".msg").html('');
                        }, 8000);
                    } else if (data == 4) {
                        $("#submit").attr('disabled', false);
                        $("#submit").html('REGISTER');
                        $(".msg").html('<span style="color:red">Invalid Refferal code!</span>');
                        setTimeout(function() {
                            $(".msg").html('');
                        }, 8000);
                    } else if (data == 5) {
                        $("#submit").attr('disabled', false);
                        $("#submit").html('REGISTER');
                        $(".msg").html('<span style="color:red">Invalid OTP!</span>');
                        setTimeout(function() {
                            $(".msg").html('');
                        }, 8000);
                    }
                },
            });
            e.preventDefault();
        });
    </script>

    <script>
        function isNumber(evt) {
            var iKeyCode = (evt.which) ? evt.which : evt.keyCode
            if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
                return false;

            return true;
        }

        function isDecimal(evt, obj) {

            var charCode = (evt.which) ? evt.which : event.keyCode
            var value = obj.value;
            var dotcontains = value.indexOf(".") != -1;
            if (dotcontains)
                if (charCode == 46) return false;
            if (charCode == 46) return true;
            if (charCode > 31 && (charCode < 48 || charCode > 57))
                return false;
            return true;
        }
    </script>

    <script>
        var password = document.getElementById("password"),
            confirm_password = document.getElementById("confirm_password");

        function validatePassword() {
            if (password.value != confirm_password.value) {
                confirm_password.setCustomValidity("Passwords Don't Match");
            } else {
                confirm_password.setCustomValidity('');
            }
        }

        password.onchange = validatePassword;
        confirm_password.onkeyup = validatePassword;
    </script>

    <script>
        $("#send_code").click(function() {
            var email = $("#email").val();
            if (email != "") {
                $("#send_code").html('<i class="spinner-border align-self-center" style="width:14px;height:14px"></i> sending...');
                $.ajax({
                    url: 'ajax/send_otp.php',
                    type: 'post',
                    data: {
                        'email': email
                    },
                    success: function(data) {
                        if (data == 1) {
                            $("#send_code").html('GET CODE');
                            $(".email_msg").html('<span style="color:green;">OTP send!</span>')
                        }
                    },
                });

            }
        });


        function show_pass(inputId) {
            var inputElement = document.getElementById(inputId);
            var eyeIcon = inputElement.parentElement.querySelector('.eye_icon i');

            if (inputElement.type === "password") {
                inputElement.type = "text";
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                inputElement.type = "password";
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }
    </script>

</body>

</html>