<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Registration Page</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        body {
            background-color: #bea2d9;
        }

        .registration-container {
            max-width: 300px;
            margin: 0 auto;
            padding: 20px;
            margin-top: 50px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff;
            border-radius: 8px;
        }

        .registration-container h2 {
            color: #823fb5;
            margin-bottom: 30px;
            text-align: center;
            font-size: 22px;
            font-weight: bold;
        }

        .form-group {
            position: relative;
            margin-bottom: 20px;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ced4da;
            padding-left: 40px;
            box-sizing: border-box;
        }

        .form-group i {
            position: absolute;
            left: 10px;
            top: 12px;
            color: #823fb5;
        }

        .checkbox-group {
            margin-top: 20px;
        }

        .checkbox-group label {
            display: block;
            position: relative;
            padding-left: 30px;
            cursor: pointer;
            font-size: 12px;
        }

        .checkbox-group input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }

        .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 20px;
            width: 20px;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 3px;
        }

        .checkbox-group input:checked~.checkmark {
            background-color: #823fb5;
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
        }

        .checkbox-group input:checked~.checkmark:after {
            display: block;
        }

        .checkbox-group .checkmark:after {
            left: 7px;
            top: 3px;
            width: 6px;
            height: 12px;
            border: solid white;
            border-width: 0 3px 3px 0;
            transform: rotate(45deg);
        }

        .register-link {
            text-align: center;
            margin-top: 15px;
            font-size: 13px;
        }

        .register-link a {
            color: #823fb5;
        }

        .btn-register {
            background-color: #823fb5;
            border-color: #823fb5;
            width: 100%;
            border-radius: 20px;
        }

        .btn-register:hover {
            background: none;
            border: 1px solid #823fb5;
            color: #823fb5;
        }

        .form-control {
            border-radius: 20px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="registration-container">
            <h2>Register</h2>
            <form id="login_form" method="post">
                <div class="form-group">
                    <i class="fas fa-link"></i>
                    <input type="text" class="form-control" id="referralCode" name="refferal_code" placeholder="Enter a refferal code">
                </div>
                <div class="form-group">
                    <i class="fas fa-user"></i>
                    <input type="text" class="form-control" id="username" name="username" placeholder="Username">
                </div>
                <div class="form-group">
                    <i class="fas fa-envelope"></i>
                    <input type="email" class="form-control" id="email" name="email" placeholder="Email">
                </div>
                <div class="form-group">
                    <i class="fas fa-mobile-alt"></i>
                    <input type="tel" class="form-control" id="mobile_code" name="phone" placeholder="Mobile Number">
                </div>
                <div class="form-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" class="form-control" id="password" name="password" placeholder="Password">
                </div>
                <div class="form-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" class="form-control" id="confirmPassword" name="confirm_password" placeholder="Confirm Password">
                </div>
                <div class="checkbox-group">
                    <label>
                        <input type="checkbox" id="agreeCheckbox" name="tnc">
                        <span class="checkmark"></span> Accept our Terms and Conditions
                    </label>
                </div>
                <button type="submit" class="btn btn-primary btn-register">Register</button>
            </form>

            <form id="login_form" method="post">

            </form>
            <div class="register-link">
                <p>Already have an account ?<a href="login.php">Login</a></p>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.0.7/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>



    <script>
        $("#login_form").submit(function(e) {
            $("#submit").attr('disabled', true);
            $("#submit").html('<i class="spinner-border align-self-center" style="width="20" height="20"></i> processing...');
            $.ajax({
                url: 'ajax/signup.php',
                type: 'post',
                data: $("#login_form").serialize(),
                success: function(data) {
                    if (data == 1) {
                        $("#submit").attr('disabled', false);
                        $("#submit").html('REGISTER');
                        $(".msg").html('<span style="color:green">Registration successfully!</span>');
                        setTimeout(function() {
                            $(".msg").html('');
                        }, 8000);
                    } else if (data == 2) {
                        $("#submit").attr('disabled', false);
                        $("#submit").html('REGISTER');
                        $(".msg").html('<span style="color:red">Username not available!</span>');
                        setTimeout(function() {
                            $(".msg").html('');
                        }, 8000);
                    } else if (data == 3) {
                        $("#submit").attr('disabled', false);
                        $("#submit").html('REGISTER');
                        $(".msg").html('<span style="color:red">Phone no. not available!</span>');
                        setTimeout(function() {
                            $(".msg").html('');
                        }, 8000);
                    } else if (data == 4) {
                        $("#submit").attr('disabled', false);
                        $("#submit").html('REGISTER');
                        $(".msg").html('<span style="color:red">Invalid Refferal code!</span>');
                        setTimeout(function() {
                            $(".msg").html('');
                        }, 8000);
                    } else if (data == 5) {
                        $("#submit").attr('disabled', false);
                        $("#submit").html('REGISTER');
                        $(".msg").html('<span style="color:red">Invalid OTP!</span>');
                        setTimeout(function() {
                            $(".msg").html('');
                        }, 8000);
                    }
                },
            });
            e.preventDefault();
        });
    </script>

    <script>
        function isNumber(evt) {
            var iKeyCode = (evt.which) ? evt.which : evt.keyCode
            if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
                return false;

            return true;
        }

        function isDecimal(evt, obj) {

            var charCode = (evt.which) ? evt.which : event.keyCode
            var value = obj.value;
            var dotcontains = value.indexOf(".") != -1;
            if (dotcontains)
                if (charCode == 46) return false;
            if (charCode == 46) return true;
            if (charCode > 31 && (charCode < 48 || charCode > 57))
                return false;
            return true;
        }
    </script>

    <script>
        var password = document.getElementById("password"),
            confirm_password = document.getElementById("confirm_password");

        function validatePassword() {
            if (password.value != confirm_password.value) {
                confirm_password.setCustomValidity("Passwords Don't Match");
            } else {
                confirm_password.setCustomValidity('');
            }
        }

        password.onchange = validatePassword;
        confirm_password.onkeyup = validatePassword;
    </script>

    <script>
        $("#send_code").click(function() {
            var email = $("#email").val();
            if (email != "") {
                $("#send_code").html('<i class="spinner-border align-self-center" style="width:14px;height:14px"></i> sending...');
                $.ajax({
                    url: 'ajax/send_otp.php',
                    type: 'post',
                    data: {
                        'email': email
                    },
                    success: function(data) {
                        if (data == 1) {
                            $("#send_code").html('GET CODE');
                            $(".email_msg").html('<span style="color:green;">OTP send!</span>')
                        }
                    },
                });

            }
        })
    </script>
</body>

</html>