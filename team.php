<?php
include("./lib/auth.php");

$user_id = $user['id'];

date_default_timezone_set("Asia/Kolkata");
$date = date("Y-m-d H:i:s");
$today = date("Y-m-d");

function maskContact($contact)
{
    if (strpos($contact, '@') !== false) {
        // Masking email
        $parts = explode('@', $contact);
        $localPart = substr($parts[0], 0, 3) . str_repeat('*', 5);
        $domain = $parts[1];
        return $localPart . '@' . $domain;
    } elseif (is_numeric($contact)) {
        // Masking phone number
        if (strlen($contact) >= 10) {
            return substr($contact, 0, 3) . str_repeat('*', 4) . substr($contact, -3);
        }
        return $contact; // Return as is if not a valid phone number
    } else {
        // Masking name
        $nameParts = explode(' ', $contact);
        foreach ($nameParts as &$part) {
            if (strlen($part) > 3) {
                $part = substr($part, 0, 3) . str_repeat('*', strlen($part) - 3);
            }
        }
        return implode(' ', $nameParts);
    }
}

// Example usage:
// echo maskContact('<EMAIL>'); // Output: sun*****@gmail.com
// echo maskContact('9988776655');              // Output: 998****655
// echo maskContact('Sunny Aggarwal');          // Output: Sun** Agg****


function getCommission($receiver_id, $sender_id)
{
    global $today;

    $sql = dbQuery("SELECT SUM(`bonus_amount`) FROM tabl_bonussummery WHERE receiver_id = '$receiver_id' AND user_id = '$sender_id' AND DATE(`date`)='$today'");
    $res = dbFetchAssoc($sql);

    return $res['SUM(`bonus_amount`)'] ?? 0;
}


?>
<!doctype html>
<html lang="en">

<head>
    <meta cha$et="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Team Info</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <link rel="stylesheet" href="assets/nav.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .banner {
            /* border: 1px solid purple; */
            /* border: 1px solid purple;  */
            margin: 10px;
            background: #ffffff;
            /* border-radius: 8px; */
            box-shadow: 0 0 10px rgb(0 0 0 / 10%);
            padding: 12px;
        }

        .fullbody1 {
            background: #823fb50d;
            height: 260vh;
        }

        .icon {
            color: #823fb5;
            font-size: 38px;
            margin-bottom: 16px;
        }

        .line {
            padding: -4px;
            width: 100%;
            /* text-align: center; */
            /* justify-content: center; */
            height: 1px;
            background: black;
            margin-top: -4px;
        }

        .banner h6 {
            font-size: 15px;
            font-weight: 600;
        }


        .col {
            width: 207px;
            min-height: 40px;
        }

        .row {
            margin-bottom: -15px;
        }

        .next {
            position: absolute;
            bottom: 30px;
            text-align: center;
            justify-content: center;
        }

        .button {
            width: 90%;
            text-align: center;
            display: inline-block;
            padding: 10px 20px;
            text-decoration: none;
            background-color: orange;
            color: #fff !important;
            border: none;
            border-radius: 20px;
        }

        .next .contact {
            width: 90%;
            background: none;
            border: 1px solid #823fb5;
            color: #823fb5;
            margin-top: 20px;
        }

        .container {
            max-width: 100%;
            padding: 20px;
        }

        .rule-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 11px;
            margin-top: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .rule-card h3 {
            /* color: #823fb5; */
            color: #83d657bb;
            font-size: 17px;
        }

        .rule-card p {
            color: #666;
        }

        ul {
            color: #666;
        }



        /* Make the navigation tabs flex */
        .nav-tabs {
            display: flex;
            flex-wrap: wrap;
            overflow-x: auto;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            flex-shrink: 0;
        }

        /* Styling for the active tab */
        .nav-link.active {
            background-color: #007bff;
            color: white;
        }

        /* Add some space between the tabs */
        .nav-link {
            margin-right: 10px;
            padding: 10px 15px;
            font-size: 14px;
        }

        /* For sliding menu effect */
        #myTab {
            position: relative;
            width: 100%;
        }

        #myTabWrapper {
            overflow-x: scroll;
            padding-bottom: 5px;
            white-space: nowrap;
            box-sizing: border-box;
        }

        /* Style the tabs container for the sliding effect */
        .nav-tabs {
            display: inline-flex;
            flex-wrap: nowrap;
            overflow-x: scroll;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            min-width: 100px;
        }

        /* For sliding functionality - hide scrollbars */
        #myTabWrapper::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>

<body>
    <div class="fullbody1">
        <div class="header">
            <div class="header-items d-flex align-items-center justify-content-between m-2">
                <div class="web w-10" onclick="window.location.href = 'index.php'">


                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-arrow-left"
                        viewBox="0 0 16 16">
                        <path fill-rule="evenodd"
                            d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z" />
                    </svg>
                </div>

                <div class="home w-90 d-flex justify-content-center">
                    <h4>Team Info</h4>
                </div>

                <div class="web w-10" onclick="window.location.href = 'index.php'">


                    <!-- <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-headset" viewBox="0 0 16 16">
                        <path d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5"/>
                      </svg> -->
                </div>

            </div>
        </div>
        <div class="container">
            <div>
                <ul class="nav nav-tabs" id="myTab" role="tablist">
                    <?php
                    $record_types = ["level1", "level2", "level3", "level4", "level5", "level6", "level7", "level8", "level9", "level10"];
                    foreach ($record_types as $index => $record_type) { ?>
                        <li class="nav-item w-16" role="presentation">
                            <button class="nav-link <?= $index === 0 ? 'active' : ''; ?> w-100"
                                id="<?= $record_type; ?>-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#<?= $record_type; ?>"
                                type="button" role="tab"
                                aria-controls="<?= $record_type; ?>"
                                aria-selected="<?= $index === 0 ? 'true' : 'false'; ?>">
                                <?= ucfirst($record_type); ?>
                            </button>
                        </li>
                    <?php } ?>
                </ul>

                <div class="tab-content" id="myTabContent">
                    <?php
                    $levels = ["level1" => [$user['id']]]; // Initialize level1 with user's own code.
                    for ($i = 2; $i <= 11; $i++) {
                        $levels["level{$i}"] = []; // Initialize other levels as empty arrays.
                    }

                    foreach ($record_types as $record_type) {
                        $current_level = $levels[$record_type];
                    ?>
                        <div class="tab-pane <?= $record_type === 'level1' ? 'show active' : 'fade'; ?>"
                            id="<?= $record_type; ?>" role="tabpanel"
                            aria-labelledby="<?= $record_type; ?>-tab">
                            <?php
                            if (!empty($current_level)) {
                                foreach ($current_level as $code) {
                                    // Fetch users for the current level
                                    $sel_user = dbQuery("SELECT * FROM tabl_user WHERE upliner_id = '$code'");
                                    while ($res_user = dbFetchAssoc($sel_user)) {
                                        // Push to the next level array
                                        $next_level = "level" . (array_search($record_type, $record_types) + 2);
                                        if (isset($levels[$next_level])) {
                                            $levels[$next_level][] = $res_user['id'];
                                        }

                                        // Prepare user display information
                                        // $username = $res_user['email'] ?: ($res_user['phone'] ?: $res_user['fname'] . ' ' . $res_user['lname']);
                                        $username = $res_user['email'] ?: ($res_user['phone'] ?: $res_user['name']);
                                        // $wallet_balance = check_wallet($res_user['id']);

                                        $m_wallet = 0;

                                        // echo $res['id'];
                                        $wallet_balance = dbQuery("SELECT sum(receive_amount-paid_amount) as ttlamount FROM tabl_main_wallet WHERE user_id='" . $res_user['id'] . "'");

                                        // $wallet_balance = dbQuery("SELECT sum(receive_amount) as ttlamount FROM tabl_main_wallet WHERE user_id='" . $res['id'] . "'");
                                        if ($res_wallet_amount = dbFetchAssoc($wallet_balance)) {
                                            if ($res_wallet_amount['ttlamount'] == "") {
                                                $m_wallet = 0;
                                            } else {
                                                $m_wallet = $res_wallet_amount['ttlamount'];
                                            }
                                        }

                            ?>
                                        <div class="alert2 d-flex align-items-center justify-content-around p-2 mt-2 mb-1 border-bottom-1">
                                            <div class="container-fluid">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div class="energy">
                                                        <!-- <p class="mb-0"><?= htmlspecialchars($res_user['id']); ?></p> -->
                                                        <p class="mb-0"><?= htmlspecialchars($res_user['sponsor_id']); ?></p>
                                                    </div>
                                                    <div class="energy">
                                                        <p class="mb-0"><?= $currencySymbols; ?> <?= number_format($m_wallet, 2); ?></p>
                                                    </div>
                                                    <div class="energy">
                                                        <p class="mb-0"><?= date("d-m-y", strtotime($res_user['date_added'])); ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                            <?php
                                    }
                                }
                            } else {
                                echo "<p class='text-center mt-3'>No data found for this level.</p>";
                            }
                            ?>
                        </div>
                    <?php
                    }

                    // echo '<pre>';
                    // print_r($levels);
                    // echo '</pre>';
                    ?>
                </div>

            </div>


        </div>
















    </div>




    <script src="assets/nav.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous">
    </script>
</body>

</html>