<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 3;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

if (isset($_REQUEST['submit'])) {
  if ($_REQUEST['submit'] == 'login') {
    $password       =   mysqli_real_escape_string($con, $_POST['password']);
    $con_password   =   mysqli_real_escape_string($con, $_POST['confirm_password']);

    if ($password == $con_password) {
      $pass = password_hash($password, PASSWORD_BCRYPT);
      dbQuery("UPDATE tabl_user SET `password`='$pass' WHERE id='" . $_REQUEST['id'] . "'");
      echo '<script>alert("Login password changed successfully!");window.location.href="users.php"</script>';
    } else {
      echo '<script>alert("Both password are not match!");window.location.href="users.php"</script>';
    }
  } else if ($_REQUEST['submit'] == 'withdraw') {
    $fund_password  =   mysqli_real_escape_string($con, $_POST['fund_password']);
    $con_fund_password  =   mysqli_real_escape_string($con, $_POST['con_fund_password']);

    if ($fund_password == $con_fund_password) {
      $fund_pass = password_hash($fund_password, PASSWORD_BCRYPT);
      dbQuery("UPDATE tabl_user SET `fund_password`='$fund_pass' WHERE id='" . $_REQUEST['id'] . "'");
      echo '<script>alert("Withdraw password changed successfully!");window.location.href="users.php"</script>';
    } else {
      echo '<script>alert("Both password are not match!");window.location.href="users.php"</script>';
    }
  }
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
  <title><?php echo SITE; ?>- User Change Password</title>
  <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
  <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
  <script src="assets/js/loader.js"></script>

  <!-- BEGIN GLOBAL MANDATORY STYLES -->
  <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
  <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
  <!-- END GLOBAL MANDATORY STYLES -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
  <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
  <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
  <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

  <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
  <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">

  <style>
    .pass {
      font-size: 16px;
      font-weight: 600;
    }

    .eye_icon {
      border-radius: 0px 5px 5px 0px !important;
    }
  </style>
</head>

<body class="alt-menu sidebar-noneoverflow">
  <?php include('inc/__header.php');

  //print_r($_SESSION);


  ?>
  <!--  BEGIN MAIN CONTAINER  -->
  <div class="main-container" id="container">
    <div class="overlay"></div>
    <div class="search-overlay"></div>

    <!--  BEGIN TOPBAR  -->
    <?php include('inc/__menu.php'); ?>
    <!--  END TOPBAR  -->

    <!--  BEGIN CONTENT PART  -->
    <div id="content" class="main-content">
      <div class="layout-px-spacing">
        <nav class="breadcrumb-one" aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">Home</a></li>
            <li class="breadcrumb-item"><a href="users.php">Users</a></li>
            <li class="breadcrumb-item active"><a href="javascript:void(0);">User Change Password</a></li>
          </ol>
        </nav>
        <div class="account-settings-container layout-top-spacing">
          <div class="account-content">
            <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
              <div class="row">
                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing section general-info">
                  <div class="info">
                    <h6 class="">User Change Password</h6>
                    <div class="row">
                      <div class="col-lg-11 mx-auto">
                        <div class="row">
                          <div class="col-xl-12 col-lg-12 col-md-8 mt-md-0 ">
                            <div class="widget-content widget-content-area">
                              <form name="account" method="post" id="general-info" enctype="multipart/form-data">
                                <p class="pass">Login Password</p>
                                <div class="form-row mb-6">
                                  <div class="form-group col-md-4">
                                    <label for="password"><strong>Password</strong></label>


                                    <div class="input-group mb-3">
                                      <input type="password" class="form-control" id="password" name="password" placeholder="Enter Login Password" required>
                                      <div class="input-group-prepend">
                                        <span class="input-group-text eye_icon pointer" onclick="show_pass('password');">
                                          <i class="fa fa-eye" aria-hidden="true"></i>
                                        </span>
                                      </div>
                                    </div>

                                    <!-- <input type="password" name="password" id="password" class="form-control" placeholder="Enter Login Password" required> -->
                                  </div>

                                  <div class="form-group col-md-4">
                                    <label for="confirm_password"><strong>Confirm Password</strong></label>

                                    <div class="input-group mb-3">
                                      <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Confirm Password" required>
                                      <div class="input-group-prepend">
                                        <span class="input-group-text eye_icon pointer" onclick="show_pass('confirm_password');">
                                          <i class="fa fa-eye" aria-hidden="true"></i>
                                        </span>
                                      </div>
                                    </div>

                                    <!-- <input type="password" name="confirm_password" id="confirm_password" class="form-control" placeholder="Confirm Password" required> -->
                                  </div>
                                </div>
                                <div class="form-row">
                                  <button type="submit" name="submit" value="login" class="btn btn-primary ">Submit</button>
                                </div>
                              </form>
                            </div>
                          </div>
                        </div>

                        <div class="row">
                          <div class="col-xl-12 col-lg-12 col-md-8 mt-md-0 ">
                            <div class="widget-content widget-content-area">
                              <form name="account" method="post" id="general-info" enctype="multipart/form-data">
                                <p class="pass">Withdraw Password</p>
                                <div class="form-row mb-6">
                                  <div class="form-group col-md-4">
                                    <label for="fund_password"><strong>Password</strong></label>

                                    <div class="input-group mb-3">
                                      <input type="password" class="form-control" id="fund_password" name="fund_password" placeholder="Enter Withdraw Password" required>
                                      <div class="input-group-prepend">
                                        <span class="input-group-text eye_icon pointer" onclick="show_pass('fund_password');">
                                          <i class="fa fa-eye" aria-hidden="true"></i>
                                        </span>
                                      </div>
                                    </div>

                                    <!-- <input type="password" name="fund_password" id="fund_password" class="form-control" placeholder="Enter Withdraw Password" required> -->
                                  </div>

                                  <div class="form-group col-md-4">
                                    <label for="con_fund_password"><strong>Confirm Password</strong></label>

                                    <div class="input-group mb-3">
                                      <input type="password" class="form-control" id="con_fund_password" name="con_fund_password" placeholder="Confirm Withdraw Password" required>
                                      <div class="input-group-prepend">
                                        <span class="input-group-text eye_icon pointer" onclick="show_pass('con_fund_password');">
                                          <i class="fa fa-eye" aria-hidden="true"></i>
                                        </span>
                                      </div>
                                    </div>

                                    <!-- <input type="password" name="con_fund_password" id="con_fund_password" class="form-control" placeholder="Confirm Withdraw Password" required> -->
                                  </div>
                                </div>
                                <div class="form-row">
                                  <button type="submit" name="submit" value="withdraw" class="btn btn-primary ">Submit</button>
                                </div>
                              </form>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <?php include('inc/__footer.php'); ?>
      </div>
    </div>
    <!--  END CONTENT PART  -->

  </div>
  <!-- END MAIN CONTAINER -->

  <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
  <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
  <script src="bootstrap/js/popper.min.js"></script>
  <script src="bootstrap/js/bootstrap.min.js"></script>
  <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
  <script src="assets/js/app.js"></script>
  <script>
    $(document).ready(function() {
      App.init();
    });
  </script>
  <script src="assets/js/custom.js"></script>
  <!-- END GLOBAL MANDATORY SCRIPTS -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
  <script src="plugins/apex/apexcharts.min.js"></script>
  <script src="assets/js/dashboard/dash_2.js"></script>
  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

  <script src="plugins/dropify/dropify.min.js"></script>
  <script src="plugins/blockui/jquery.blockUI.min.js"></script>
  <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
  <script src="assets/js/users/account-settings.js"></script>

  <script>
    var password = document.getElementById("password"),
      confirm_password = document.getElementById("confirm_password");

    function validatePassword() {
      if (password.value != confirm_password.value) {
        confirm_password.setCustomValidity("Passwords Don't Match");
      } else {
        confirm_password.setCustomValidity('');
      }
    }
    password.onchange = validatePassword;
    confirm_password.onkeyup = validatePassword;


    function show_pass(inputId) {
      var inputElement = document.getElementById(inputId);
      var eyeIcon = inputElement.parentElement.querySelector('.eye_icon i');

      if (inputElement.type === "password") {
        inputElement.type = "text";
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
      } else {
        inputElement.type = "password";
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
      }
    }
  </script>
</body>

</html>