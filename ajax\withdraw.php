<?php
session_start();
include('../admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

/*amount: 4
trc_address: dfsfsdfdf45654ytrytrytrytry
email: <EMAIL>*/

$wallet_balance = dbQuery("SELECT sum(receive_amount-paid_amount) as ttlamount FROM tabl_main_wallet WHERE user_id='" . $_SESSION['user_id'] . "'");
$res_wallet_amount = dbFetchAssoc($wallet_balance);
if ($res_wallet_amount['ttlamount'] != "") {
	$m_wallet = $res_wallet_amount['ttlamount'];
} else {
	$m_wallet = '0.00';
}


$up_bal = round($m_wallet, 2);
$deduct = $_REQUEST['amount'] * W_CHARGES / 100;
// $deduct1 = $_REQUEST['amount'] * S_CHARGES / 100;
$deduct1 = S_CHARGES;
$total_deduct = (float)$deduct + (float)$deduct1;


$amount = round((float)$_REQUEST['amount'] - (float)$total_deduct, 2);



if ($m_wallet < $_REQUEST['amount']) {
	echo '<span style="color:red">Insufficient Wallet Balance!</span>';
	die();
} else {
	if ($_REQUEST['amount'] < M_WITHRAW) {
		echo '<span style="color:red">Minimum withdraw amount is $' . M_WITHRAW . '!</span>';
		die();
	} else {

		$fund_password = $_REQUEST['fund_password'];
		$userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
		$user = dbFetchAssoc($userq);

		$passcheck = password_verify($fund_password, $user['fund_password']);

		if ($passcheck == 0) {
			echo '<span style="color:red">Invalid Fund Password!</span>';
			die();
		} else {
			dbQuery("INSERT INTO tabl_withdrawl SET user_id='" . $_SESSION['user_id'] . "',amount='" . $amount . "',wallet_address='" . $_REQUEST['wallet_address'] . "',requested_at='" . $date . "',status=0");
			$wid = dbInsertId();

			dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $_SESSION['user_id'] . "',paid_amount='" . $_REQUEST['amount'] . "',type=2,type_id=5,w_id='" . $wid . "',narration='Withdraw Request',date_added='" . $date . "'");

			$_SESSION['wid'] = $wid;
			echo 1;
		}
	}
}
