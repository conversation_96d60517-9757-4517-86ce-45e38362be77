<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 14;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');
if (isset($_REQUEST['submit'])) {

  if ($_FILES["image"]["name"] != "") {
    $target_dir = "../assets/image/news/";
    $name = rand(10000, 1000000);
    $extension = pathinfo($_FILES["image"]["name"], PATHINFO_EXTENSION);
    $new_name = $name . "." . $extension;
    $target_file = $target_dir . $name . "." . $extension;

    $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
    if ($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg") {
      die("This is not valid image. Please try again.");
    } else {
      move_uploaded_file($_FILES["image"]["tmp_name"], $target_file);
      $image_name = $new_name;
    }
  } else {
    $image_name = '';
  }


  if ($_FILES["video"]["name"] != "") {
    $target_dir = "../assets/image/news/";
    $name = rand(10000, 1000000);
    $extension = pathinfo($_FILES["video"]["name"], PATHINFO_EXTENSION);
    $new_name1 = $name . "." . $extension;
    $target_file = $target_dir . $name . "." . $extension;

    $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
    if ($imageFileType != "mp4" && $imageFileType != "avi" && $imageFileType != "wmv" && $imageFileType != "mov") {
      die("This is not valid Video. Please try again.");
    } else {
      move_uploaded_file($_FILES["video"]["tmp_name"], $target_file);
      $video_name = $new_name1;
    }
  } else {
    $video_name = '';
  }

  dbQuery("INSERT INTO tabl_news SET title='" . mysqli_real_escape_string($con, $_REQUEST['title']) . "',description='" . mysqli_real_escape_string($con, $_REQUEST['description']) . "',image='" . $image_name . "',video='" . $video_name . "',date_added='" . $date . "'");

  echo '<script>alert("News Added Successfully!");window.location.href="news.php"</script>';
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
  <title><?php echo SITE; ?>- News Room</title>
  <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
  <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
  <script src="assets/js/loader.js"></script>
  <script src="https://cdn.ckeditor.com/4.22.1/standard/ckeditor.js"></script>

  <!-- BEGIN GLOBAL MANDATORY STYLES -->
  <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
  <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
  <!-- END GLOBAL MANDATORY STYLES -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
  <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
  <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
  <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

  <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
  <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
</head>

<body class="alt-menu sidebar-noneoverflow">
  <?php include('inc/__header.php'); ?>
  <!--  BEGIN MAIN CONTAINER  -->
  <div class="main-container" id="container">
    <div class="overlay"></div>
    <div class="search-overlay"></div>

    <!--  BEGIN TOPBAR  -->
    <?php include('inc/__menu.php'); ?>
    <!--  END TOPBAR  -->

    <!--  BEGIN CONTENT PART  -->
    <div id="content" class="main-content">
      <div class="layout-px-spacing">
        <nav class="breadcrumb-one" aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="home.php">Home</a></li>
            <li class="breadcrumb-item"><a href="javascript:void(0);">News Room</a></li>
            <li class="breadcrumb-item active"><a href="javascript:void(0);">Add News</a></li>
          </ol>
        </nav>
        <div class="account-settings-container layout-top-spacing">
          <div class="account-content">
            <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
              <div class="row">
                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">

                  <form name="account" method="post" id="general-info" class="section general-info" enctype="multipart/form-data">
                    <div class="info">
                      <h6 class="">News Room : ADD</h6>
                      <div class="row">
                        <div class="col-lg-12 mx-auto">
                          <div class="row">
                            <div class="col-xl-12 col-lg-12 col-md-8 mt-md-0 mt-4">
                              <div class="form-row mb-4">
                                <div class="form-group col-md-12">
                                  <label for="inputEmail4"><strong>Title</strong></label>
                                  <input type="text" class="form-control mb-4" id="title" name="title" placeholder="Title" required>
                                </div>
                                <div class="form-group col-md-12">
                                  <label for="inputEmail4"><strong>Description</strong></label>
                                  <textarea name="description" class="form-control mb-4" id="description" placeholder="Description" rows="5" required></textarea>
                                </div>

                                <div class="form-group col-md-6">
                                  <label for="inputEmail4"><strong>Image</strong></label>
                                  <input type="file" class="form-control mb-4" id="Image" name="image" placeholder="Image">
                                </div>



                                <div class="form-group col-md-6">
                                  <label for="inputEmail4"><strong>Video</strong></label>
                                  <input type="file" class="form-control mb-4" id="video" name="video" placeholder="Video">
                                </div>




                              </div>
                              <div class="form">
                                <div class="row">
                                  <div class="col-sm-12">
                                    <div class="form-group">
                                      <label for="fullName"></label>
                                      <button type="submit" name="submit" class="btn btn-secondary mb-4 mr-2">Submit</button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
        <?php include('inc/__footer.php'); ?>
      </div>
    </div>
    <!--  END CONTENT PART  -->

  </div>
  <!-- END MAIN CONTAINER -->

  <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
  <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
  <script src="bootstrap/js/popper.min.js"></script>
  <script src="bootstrap/js/bootstrap.min.js"></script>
  <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
  <script src="assets/js/app.js"></script>
  <script>
    $(document).ready(function() {
      App.init();
    });
  </script>
  <script src="assets/js/custom.js"></script>
  <!-- END GLOBAL MANDATORY SCRIPTS -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
  <script src="plugins/apex/apexcharts.min.js"></script>
  <script src="assets/js/dashboard/dash_2.js"></script>
  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

  <script src="plugins/dropify/dropify.min.js"></script>
  <script src="plugins/blockui/jquery.blockUI.min.js"></script>
  <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
  <script src="assets/js/users/account-settings.js"></script>
</body>

</html>
<script>
  function isNumber(evt) {
    var iKeyCode = (evt.which) ? evt.which : evt.keyCode
    if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
      return false;

    return true;
  }

  function isDecimal(evt, obj) {

    var charCode = (evt.which) ? evt.which : event.keyCode
    var value = obj.value;
    var dotcontains = value.indexOf(".") != -1;
    if (dotcontains)
      if (charCode == 46) return false;
    if (charCode == 46) return true;
    if (charCode > 31 && (charCode < 48 || charCode > 57))
      return false;
    return true;
  }
</script>