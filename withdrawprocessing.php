<?php
@session_start();
include('./lib/auth.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

if (!isset($_SESSION['wid']) || $_SESSION['wid'] == '') {
    echo "<script>window.location.href = 'withdraw.php';</script>";
}

$wid = $_SESSION['wid'];
unset($_SESSION['wid']);

$sel_withdraw = dbQuery("SELECT * FROM tabl_withdrawl WHERE id='" . $wid . "'");

if (!$res_withdraw = dbFetchAssoc($sel_withdraw)) {
    echo "<script>window.location.href = 'withdraw.php';</script>";
}

$sel_network = dbQuery("SELECT * FROM tabl_bank_details WHERE `id`='" . $res_withdraw['wallet_address'] . "'");
if ($res_network = dbFetchAssoc($sel_network)) {
    $network_name = $res_network['network_name'];
    $wallet_address = $res_network['wallet_address'];
} else {
    $network_name = "";
    $wallet_address = "";
}

?>

<!doctype html>
<html lang="en">

<head>
    <meta cha$et="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Withdraw processing</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <link rel="stylesheet" href="assets/nav.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .banner {
            /* border: 1px solid purple; */
            /* border: 1px solid purple;  */
            margin: 10px;
            background: #ffffff;
            /* border-radius: 8px; */
            box-shadow: 0 0 10px rgb(0 0 0 / 10%);
            padding: 12px;
        }

        .icon {
            color: #823fb5;
            font-size: 38px;
            margin-bottom: 16px;
        }

        .line {
            padding: -4px;
            width: 100%;
            /* text-align: center; */
            /* justify-content: center; */
            height: 1px;
            background: black;
            margin-top: -4px;
        }

        .banner h6 {
            font-size: 15px;
            font-weight: 600;
        }


        .col {
            width: 207px;
            min-height: 40px;
        }

        .row {
            margin-bottom: -15px;
        }

        .next {
            position: absolute;
            bottom: 30px;
            text-align: center;
            justify-content: center;
        }

        .button {
            width: 90%;
            text-align: center;
            display: inline-block;
            padding: 10px 20px;
            text-decoration: none;
            background-color: orange;
            color: #fff !important;
            border: none;
            border-radius: 20px;
        }

        /* .next .contact {
            width: 90%;
            background: none;
            border: 1px solid #823fb5;
            color: #823fb5;
            margin-top: 20px;
        } */

        .next .contact {
            width: 90%;
            text-align: center;
            display: inline-block;
            padding: 10px 20px;
            text-decoration: none;
            background-color: none;
            color: #823fb5 !important;
            border: 1px solid #823fb5;
            border-radius: 20px;
        }
    </style>
</head>

<body>
    <div class="fullbody1">
        <div class="header">
            <div class="header-items d-flex align-items-center justify-content-between m-2">
                <div class="web w-10" onclick="window.location.href = 'index.php'">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-arrow-left" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z" />
                    </svg>
                </div>

                <div class="home w-90 d-flex justify-content-center">
                    <h4>Withdraw</h4>
                </div>

                <div class="web w-10" onclick="window.location.href = 'index.php'">
                    <!-- <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-headset" viewBox="0 0 16 16">
                        <path d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5"/>
                    </svg> -->
                </div>

            </div>
        </div>
        <div class="heading text-center m-4">
            <i class="icon fas fa-check-circle"></i>
            <h2 style="font-size:19px;">Processing Withdrawal</h2>
        </div>

        <div class="banner">
            <div class="row p-1">
                <div class="col">
                    <h6>Order Number</h6>
                </div>
                <div class="col ">
                    <!-- <p class="text-end">202311090644174 12700 -->
                    <p class="text-end"><?php echo date("YmdHi", strtotime($res_withdraw['requested_at'])) . sprintf('%04d', $res_withdraw['id']);  ?>

                    </p>
                </div>
            </div>
            <div class="row p-2">
                <div class="col">
                    <h6>Time</h6>
                </div>
                <div class="col ">
                    <p class="text-end"><?php echo date("Y-m-d H:i:s", strtotime($res_withdraw['requested_at'])); ?>

                    </p>
                </div>
            </div>
            <div class="row p-2">
                <div class="col">
                    <h6>Withdrawal amount</h6>
                </div>
                <div class="col ">
                    <p class="text-end">
                        <?php
                        $base = $res_withdraw['amount'] + S_CHARGES;
                        // $base = $res_withdraw['amount'];
                        // $percentage = 0.92;
                        $percentage = (100 - W_CHARGES) / 100;

                        // $result = ($base / $percentage) - 2;
                        $result = ($base / $percentage);

                        echo $result;
                        ?>

                    </p>
                </div>
            </div>
            <hr class="line" />
            <div class="row p-2">
                <div class="col">
                    <h6>Mainnet Type</h6>
                </div>
                <div class="col ">
                    <p class="text-end"><?php echo $network_name; ?></p>
                </div>
            </div>
            <div class="row p-2">
                <div class="col">
                    <h6>Wallet Address</h6>
                </div>
                <div class="col ">
                    <p class="text-end"><?php echo $wallet_address; ?></p>

                </div>

            </div>
        </div>

        <div class="next mt-4">

            <a href="index.php" class="button">Back To Home Page</a>
            <a href="service.php" class="contact my-2">contact Customer Service</a>
            <!-- <button class='contact'>contact Customer Service</button> -->
        </div>
    </div>




    <script src="assets/nav.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
</body>

</html>