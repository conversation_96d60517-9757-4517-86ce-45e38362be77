<?php
session_start();
include('lib/auth.php');
// include('admin/lib/db_connection.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

$sel = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
$res = dbFetchAssoc($sel);

$plan = dbQuery("SELECT * FROM tabl_plans WHERE id='" . $_REQUEST['plan_id'] . "'");
$res_plan = dbFetchAssoc($plan);
$order_id = 'GET' . rand(10000000, 99999999);

// $curl = curl_init();
// curl_setopt_array($curl, array(
//   CURLOPT_URL => 'https://api.nowpayments.io/v1/invoice',
//   CURLOPT_RETURNTRANSFER => true,
//   CURLOPT_ENCODING => '',
//   CURLOPT_MAXREDIRS => 10,
//   CURLOPT_TIMEOUT => 0,
//   CURLOPT_FOLLOWLOCATION => true,
//   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
//   CURLOPT_CUSTOMREQUEST => 'POST',
//   CURLOPT_POSTFIELDS => '{
//   "price_amount": "' . $res_plan['price'] . '",
//   "price_currency": "usd",
//   "order_id": "' . $order_id . '",
//   "order_description": "Plan Purchase",
//   "ipn_callback_url": "https://nowpayments.io",
//   "success_url": "https://webdori.in/mlm_app/success.php",
//   "cancel_url": "https://webdori.in/mlm_app/cancel.php"
// }',
//   CURLOPT_HTTPHEADER => array(
//     'x-api-key: MT47JQE-JEZ4Z4S-KSTQQZ5-KD9HDBA',
//     'Content-Type: application/json'
//   ),
// ));

// $response = curl_exec($curl);
// curl_close($curl);

// $json = json_decode($response, true);


$json['id'] = 1;
$json['order_id'] = $order_id;
$json['order_description'] = 'Test';
$json['price_amount'] = $res_plan['price'];

$_SESSION['order_id'] = $order_id;

$status = dbQuery("INSERT INTO tabl_payment_transaction SET user_id='" . $_SESSION['user_id'] . "',plan_id='" . $_REQUEST['plan_id'] . "',invoice_id='" . $json['id'] . "',order_id='" . $json['order_id'] . "',order_description='" . $json['order_description'] . "',amount='" . $json['price_amount'] . "',date_added='" . $date . "'");

echo "INSERT INTO tabl_payment_transaction SET user_id='" . $_SESSION['user_id'] . "',plan_id='" . $_REQUEST['plan_id'] . "',invoice_id='" . $json['id'] . "',order_id='" . $json['order_id'] . "',order_description='" . $json['order_description'] . "',amount='" . $json['price_amount'] . "',date_added='" . $date . "'";

// $payment_url = $json['invoice_url'];
// echo '<script> setTimeout(function(){ window.location = "' . $payment_url . '" },0); </script>';

if ($status) {
  // echo '<script>alert("Request Success!");setTimeout(function(){ window.location = "success.php?plan_id=' . $_REQUEST['plan_id'] . '" },0); </script>';

  // echo '<script>setTimeout(function(){ window.location = "success.php?plan_id=' . $_REQUEST['plan_id'] . '" },0); </script>';
} else {
  // echo '<script>alert("Someting went wrong!");setTimeout(function(){ window.location = "cancel.php" },0); </script>';

  // echo '<script>setTimeout(function(){ window.location = "cancel.php" },0); </script>';
}
