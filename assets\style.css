@import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css");
/* @import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap'); */
@import url('https://fonts.googleapis.com/css2?family=Albert+Sans:ital,wght@0,100..900;1,100..900&display=swap');
body{
    /* font-family: "Poppins", sans-serif !important; */
    font-family: "Albert Sans", sans-serif !important;
}
.fullbody{
    background: #823fb50d;
    height: 100%;
}
i{
    font-size: 24px;
    color: white;
}
.fullbody1{
    background: #823fb50d;
    height: 100vh;
}
.header{
    /* background: #823fb5; */
    background: linear-gradient(45deg, #1ab9b7, #84d657) !important;
    padding: 20px 0  !important;
}
.home h4{
    margin-bottom: 0px;
    color: white;
    font-size: 18px;
}
.task h4{
    font-size: 20px;
}
.logo{
    margin: 10px 15px;
    background: white;
    padding: 10px;
}
.logo1{
    width: 45px;
}
.logo1 svg{
    height: 25px;
    width: 25px;
}
.web svg{
    height: 25px;
    width: 25px;
}
.logo1 img{
    width: 100%;
    border-radius: 50%;
}
.client-name{
    margin-left: 10px;
}
.name p{
    font-size: 15px;
    font-weight: 500;
}
.refresh p{
    font-size: 13px;
    margin-bottom: 0px;
}
.assets p{
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 0px;
}
.assetsprice p{
    font-size: 20px;
    font-weight: 500;
}
.today p{
    text-align: center;
    margin-bottom: 0px;
    font-size: 15px;
    font-weight: 600;
}
.deposit-row{
    margin: 20px;
    padding: 10px;
}
.deposit-name p{
    text-align: center;
    font-size: 15px;
    font-weight: 500;
}
.deposit .icons4{
    background: #823fb5;
    height: 50px;
    width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;

}
.deposit .icons{
    background: #F44336;
    height: 50px;
    width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
  
}
.deposit .icons1{
    background: #FF9800;
    height: 50px;
    width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
 
}
.deposit .icons2{
    background: #823fb5;
    height: 50px;
    width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
 
}
.deposit .icons3{
    background: #4CAF50;
    height: 50px;
    width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
  
}
.deposit .icons5{
    background: #4CAF50;
    height: 50px;
    width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;

}
.deposit .icons7{
    background: #FF9800;
    height: 50px;
    width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;

}
.deposit .icons6{
    background: #F44336;
    height: 50px;
    width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
   
}
.deposit svg{
    height: 25px;
    width: 25px;
    color: white;
}
.hamber {
    position: absolute;
    background: white;
    padding: 4px;
    border-radius: 50%;
    right: -8px;
    bottom: -3px;
}
.mr-0{
    margin-right: 0px;
}
.footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    border-top: 2px solid white;
    padding: 10px;
}a {
    color: initial !important;
}
a {
    text-decoration: none !important;
}
.fontsize18{
    font-size: 15px;
}
.dashboard svg{
    height: 20px;
    width: 20px;
}
/* .dashboard svg:hover{color: #4CAF50;} */
/* .dashboard p:hover{ color: #4CAF50;} */
.footer > a:hover svg,p{fill: #55c981 !important;}
.dashboard:hover p{color: #4CAF50;}
/* .footer > a:hover p{fill: #55c981;} */
.logo2 {
    margin: 5px 15px;
    background: white;
    padding: 12px !important;
}
.client-name1 {
    margin-left: 15px !important;
}
.btn{
    color: white !important;
    font-weight: 500 !important;
    background: #FF9800 !important;
    border: 1px solid #FF9800 !important;
    font-size: 13px !important;
}
.btn1{
    color: white !important;
    font-weight: 500 !important;
    background: #823fb5 !important;
    border: 1px solid #823fb5 !important;
}
.btn2{
    color: white !important;
    font-weight: 500 !important;
    background: red !important;
    border: 1px solid red !important;
}
.w-10{
    width: 10%;
}
.w-90{
    width: 90%;
}
.colorgreen{
/* color: #823fb5; */
color: #83d657bb;
}
.rank1{
    background: #ffd70052;
    width: max-content;
    padding: 2px 5px;
    border-radius: 5px;

}
.logo3{
    width: 25%;
}
.logo3 img {
    width: 100%;
    border-radius: 5px;
}
.mt10 {
    margin-top: 10px !important;
}
.name1 p {
    font-size: 13px;
    font-weight: 500;
}
.btn4{
    
        color: white !important;
        font-weight: 500 !important;
        background: #823fb5 !important;
        border: 1px solid #823fb5 !important;
        font-size: 11px !important;
        margin-top: 10px;
    
}
.hello{
    /* height: 149px; */
    height: 300px;
    width: 100%;
    /* background: #823fb5; */
    background: #f1f5f6;
    display: flex;
    padding: 26px 20px;
    border-radius: 0px 0px 15px 15px;
}
.hello .helloimg{
    height: 100%;
    width: 100%;
}
.hello .helloimg img{
    height: 100%;
    width: 100%;
    border-radius: 15px;
}
.dashboard1{
    height: 50px;
    width: 50px;
    background: #823fb5;
    border-radius: 10px;
    transform: rotate(45deg);
    bottom: 36px;
    position: relative;
}
.deposits label{
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 10px;
}
.deposits select{
    padding: 7px 15px;
    border: none;
    border-radius: 50px;
}
.next{
width: 100%;
    margin-bottom: 15px;
}
.next button{
    width: 100%;
    border: none;
    background: #823fb5;
    color: white;
    border-radius: 50px;
    padding: 7px;
}

/* new-css */

/* re-assets */
.rewidth-col{width: 25%;}
.re-assets{width: calc(1100% - 10px);
    background: #fff;
    box-shadow: 0px 0px 5px -1px;
    border-radius: 30px;
    text-align: center;
    display: flex !important;
    justify-content: center !important;
    text-align: center;
    padding: 10px;
}
.reprices{justify-content: center !important;
    margin: 10px;}

.relogo-design{width: 100%; display: flex; justify-content: space-between; flex-wrap: wrap; box-sizing: border-box; }
.reprofits {width: 100%; display: flex;  flex-wrap: wrap ; gap: 13px; } 
.retodays{
    width: calc(25% - 10px);
    background: #f2eae1;
    box-shadow: 0px 0px 5px -1px #00000059;
    border-radius: 30px;
    text-align: center;
    flex-wrap: wrap;
    display: flex !important;
    min-height: 114px;
    justify-content: center !important;
    text-align: center;
    padding: 20px !important;

}
/* .today p{bottom: 10px !important;} */
.reprofits p{font-size: 18px !important; font-weight: 600 !important; }
.assetsprice p{color: #000 !important; font-size: 20px !important; }
.retodays p:first-child{color: #fff;}
.retodays{    background: linear-gradient(45deg, #1ab9b6af, #83d657bb) !important;
}
.logo.relogo-design{margin: 0; padding: 40px 50px; }
/* .logo.relogo-design{} */
.retodays p{width: 100%;display: block; font-size: 18px !important;}
.redeposit{margin-bottom: 20px !important; }
.rebtn-design{background-color:#ff8800 !important ; border: none !important;}
.card-body{ background: linear-gradient(45deg, #1ab9b6af, #83d657bb) !important;}
.next button{background-color:#ff8800 ;}
.button{background-color: #55c981 !important;}
.button:hover{background-color: transparent !important; border: 1px solid #55c981 ; color: #55c981 !important;}
@media screen and (max-width:991px) {
    .retodays{width: calc(50% - 10px) !important; margin-bottom: 10px; }
}
@media screen and (max-width:479px) {
    .retodays{width: 100% !important; margin-bottom: 10px; min-height: 65px; }
    .logo.relogo-design{padding: 20px 10px; }
    .retodays p{width: unset; display: inline-block;}
    .reprofits .retodays{flex-direction: row !important; justify-content: space-between !important; align-items: center;}
    .retodays p:first-child{font-size: 14px !important;}
    .retodays p{font-size: 14px !important;}
    .reprices{margin: 0 !important;}
   
    }


