<?php
session_start();
include('../lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

if ($_REQUEST['val'] == 1) {
	$sel = dbQuery("SELECT * FROM tabl_plan_transaction WHERE id='" . $_REQUEST['d_id'] . "'");
	$res = dbFetchAssoc($sel);

	$user_id = $res['user_id'];
	$plan_id = $res['plan_id'];
	$price = $res['price'];

	$earning_limit = $price * 3;
	// $self_bonus = $price * 10 / 100;
	// $self_bonus = $price * 14 / 100;

	//#####################Credit Self Bonus

	// dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $user_id . "',receive_amount='" . $self_bonus . "',type=1,type_id=1,narration='Self Bonus',date_added='" . $date . "'"); // comment due to off self bonus
	//#################### END Self Bonus

	//Refer Income

	//FIRST
	$sel_user = dbQuery("SELECT upliner_id FROM tabl_user WHERE id='" . $user_id . "'");
	$res_user = dbFetchAssoc($sel_user);

	$refer1 = dbQuery("SELECT id, upliner_id, earning_limit FROM tabl_user WHERE id='" . $res_user['upliner_id'] . "'");
	$num_refer1 = dbNumRows($refer1);

	if ($num_refer1 > 0) {
		$res_refer1 = dbFetchAssoc($refer1);
		$refer1_wallet = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_refer1['id'] . "'");
		$res_refer1_wallet = dbFetchAssoc($refer1_wallet);
		echo "test";

		if ($res_refer1_wallet['total_earning'] < $res_refer1['earning_limit']) {

			echo "test";
			$refer_bonus1 = $price * 12 / 100;

			dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer1['id'] . "',receive_amount='" . $refer_bonus1 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Referral Bonus',date_added='" . $date . "'");

			echo "INSERT INTO tabl_main_wallet SET user_id='" . $res_refer1['upliner_id'] . "',receive_amount='" . $refer_bonus1 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Referral Bonus',date_added='" . $date . "' ";
		}

// 		//END FIRST

// 		//SECOND	

// 		// $refer2 = dbQuery("SELECT id, upliner_id,earning_limit FROM tabl_user WHERE id='" . $res_refer1['upliner_id'] . "'");
// 		// $num_refer2 = dbNumRows($refer2);
// // 		if ($num_refer2 > 0) {
// // 			$res_refer2 = dbFetchAssoc($refer2);
// // 			$refer2_wallet = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_refer2['id'] . "'");
// // 			$res_refer2_wallet = dbFetchAssoc($refer2_wallet);
// // // 			if ($res_refer2_wallet['total_earning'] < $res_refer2['earning_limit']) {

// // 				$refer_bonus2 = $price * 5 / 100;

// // 				dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer2['id'] . "',receive_amount='" . $refer_bonus2 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Referral Bonus',date_added='" . $date . "'");
// // // 			}

// // 			//END SECOND

// // 			//THIRD


// // 			// $refer3 = dbQuery("SELECT id, upliner_id,earning_limit FROM tabl_user WHERE id='" . $res_refer2['upliner_id'] . "'");
// // 			// $num_refer3 = dbNumRows($refer3);
// // 			// if ($num_refer3 > 0) {
// // 			// 	$res_refer3 = dbFetchAssoc($refer3);
// // 			// 	$refer3_wallet = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_refer3['id'] . "'");
// // 			// 	$res_refer3_wallet = dbFetchAssoc($refer3_wallet);
// // 			// 	// if ($res_refer3_wallet['total_earning'] < $res_refer3['earning_limit']) {

// // 			// 		$refer_bonus3 = $price * 2 / 100;

// // 			// 		dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer3['id'] . "',receive_amount='" . $refer_bonus3 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Referral Bonus',date_added='" . $date . "'");
// // 			// 	// }

// // 			// 	//END THIRD

// // 			// 	//FOURTH



// // 			// 	// $refer4 = dbQuery("SELECT id, upliner_id,earning_limit FROM tabl_user WHERE id='" . $res_refer3['upliner_id'] . "'");
// // 			// 	// $num_refer4 = dbNumRows($refer4);
// // 			// 	// if ($num_refer4 > 0) {
// // 			// 	// 	$res_refer4 = dbFetchAssoc($refer4);
// // 			// 	// 	$refer4_wallet = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_refer3['upliner_id'] . "'");
// // 			// 	// 	$res_refer4_wallet = dbFetchAssoc($refer4_wallet);
// // 			// 	// 	if ($res_refer4_wallet['total_earning'] < $res_refer4['earning_limit']) {

// // 			// 	// 		$refer_bonus4 = $price * 1 / 100;

// // 			// 	// 		dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer4['upliner_id'] . "',receive_amount='" . $refer_bonus4 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Referral Bonus',date_added='" . $date . "'");
// // 			// 	// 	}
// // 			// 	// 	// END FOURTH
// // 			// 	// }
// // 			// }
// // 		}
	}
	//End REFER INCOME Here


	//Start 

	$quantum = dbQuery("SELECT * FROM tabl_user WHERE upliner_id='" . $res_user['upliner_id'] . "'");
	$new_num_quantum = dbNumRows($quantum);
	$res_quantum = dbFetchAssoc($quantum);

	$refer_wallet_info = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_user['upliner_id'] . "'");
	$res_refer_wallet_info = dbFetchAssoc($refer_wallet_info);
	if ($res_refer_wallet_info['total_earning'] < $res_quantum['earning_limit']) {

		if ($new_num_quantum >= 10 && $new_num_quantum < 30) {
			$reward = 50;
			dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_user['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 10 Direct',date_added='" . $date . "'");
		} elseif ($new_num_quantum >= 30 && $new_num_quantum < 50) {
			$reward = 100;
			dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_user['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 30 Direct',date_added='" . $date . "'");
		} elseif ($new_num_quantum >= 50 && $new_num_quantum < 70) {

			$reward = 150;
			dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_user['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 50 Direct',date_added='" . $date . "'");
		} elseif ($new_num_quantum >= 70 && $new_num_quantum < 100) {

			$reward = 200;
			dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_user['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 70 Direct',date_added='" . $date . "'");
		} elseif ($new_num_quantum >= 100) {

			$reward = 500;
			dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_user['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 100 Direct',date_added='" . $date . "'");
		}
	}
	//End Quantum Reward	

	dbQuery("UPDATE tabl_user SET earning_limit='" . $earning_limit . "',membership_id='" . $plan_id . "' WHERE id='" . $user_id . "'");
	dbQuery("UPDATE tabl_plan_transaction SET status='" . $_REQUEST['val'] . "' WHERE id='" . $_REQUEST['d_id'] . "'");
	echo 1;
} else {
	dbQuery("UPDATE tabl_plan_transaction SET status='" . $_REQUEST['val'] . "' WHERE id='" . $_REQUEST['d_id'] . "'");
	echo 2;
}