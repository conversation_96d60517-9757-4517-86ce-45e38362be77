<?php
@session_start();
include('./lib/auth.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

/// limit withdrwal 3 days 
$limitSql = dbQuery("SELECT *
FROM tabl_withdrawl
WHERE requested_at >= CURRENT_DATE - INTERVAL 3 DAY
  AND requested_at < CURRENT_DATE + INTERVAL 1 DAY
  AND user_id='" . $user['id'] . "' ORDER BY id DESC 
");
if (dbNumRows($limitSql)) {
    $dataRow = dbFetchAssoc($limitSql);
    $lastWithdrowalDate = $dataRow['requested_at'];
    echo "<script>alert('You can only make one withdrawal In 3 day. Your Last Withdrawal On " . date_format(date_create($lastWithdrowalDate), "d M, Y") . "');location.href='index.php'</script>";
    exit();
}

$sel_plan = dbQuery("SELECT * FROM tabl_plan_transaction WHERE user_id='" . $user['id'] . "' ORDER BY id DESC");
if (!$res_plan = dbFetchAssoc($sel_plan)) {
    echo '<script>alert("Please activate your account before withdrawal!");window.location.href="index.php?status=0"</script>';
}


// echo $res['id'];
$wallet_balance = dbQuery("SELECT sum(receive_amount-paid_amount) as ttlamount FROM tabl_main_wallet WHERE user_id='" . $user['id'] . "'");
$res_wallet_amount = dbFetchAssoc($wallet_balance);


$wallet_balance = ($res_wallet_amount['ttlamount'] == "") ? '0.00' : $res_wallet_amount['ttlamount'];

?>


<!doctype html>
<html lang="en">

<head>
    <meta cha$et="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Home</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <link rel="stylesheet" href="assets/nav.css">
    <style>
        .banner {
            /* border: 1px solid purple; */
            margin: 10px;
            background: #ecc7f1;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .line {
            padding: -4px;
            width: 100%;
            /* text-align: center; */
            /* justify-content: center; */
            height: 1px;
            background: black;
            margin-top: -4px;
        }

        .banner h6 {
            font-size: 15px;
            font-weight: 600;
        }

        .banner .fee {
            margin-top: -20px;
            font-weight: 500;
        }

        .fee p {
            font-style: italic;
        }

        input {
            border: none;
            border: 0.6 solid #bb3ca1;
            border-bottom: 2px solid #83d657bb;
            border-radius: 20px;
            height: 38px;


        }

        input:hover {
            border: 1 px solid #bea2d9;


        }

        .next {
            text-align: center;
        }

        .button {
            width: 100%;
            text-align: center;
            display: inline-block;
            padding: 10px 20px;
            text-decoration: none;
            background-color: #823fb5;
            color: #fff !important;
            border: none;
            border-radius: 20px;
        }
    </style>
</head>

<body>
    <div class="fullbody1">
        <div class="header">
            <div class="header-items d-flex align-items-center justify-content-between m-2">
                <div class="web w-10" onclick="window.location.href = 'index.php'">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-arrow-left" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z" />
                    </svg>
                </div>

                <div class="home w-90 d-flex justify-content-center">
                    <h4>Withdrawal</h4>
                </div>

                <div class="web w-10" onclick="window.location.href = 'index.php'">
                    <!-- <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="white" class="bi bi-headset" viewBox="0 0 16 16">
                        <path d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5"/>
                    </svg> -->
                </div>

            </div>
        </div>
        <div class="banner">
            <div class="row px-2 pt-2">
                <div class="col">
                    <!-- <h6>Withdrawal Amount</h6> -->
                    <h6>Wallet Balance</h6>
                </div>
                <div class="col">
                    <!-- <p class="text-end m-0">$ <?php // echo $wallet_balance; 
                                                    ?></p> -->
                    <p class="text-end m-0">
                        <?php

                        $price = 0;
                        $sel_plan = dbQuery("SELECT * FROM tabl_plan_transaction WHERE user_id='" . $_SESSION["user_id"] . "' ORDER BY id DESC");
                        if ($res_plan = dbFetchAssoc($sel_plan)) {
                            $price = $res_plan['price'];
                        }


                        if ($res_wallet_amount['ttlamount'] == "") {
                            echo $currencySymbols . ' ' . $price;
                        } else {
                            echo $currencySymbols . ' ' . $price + $res_wallet_amount['ttlamount'];
                        }
                        ?>
                    </p>
                </div>

            </div>
            <div class="row px-2">
                <div class="col">
                    <!-- <h6>Withdrawal Amount</h6> -->
                    <h6>Net Amount</h6>
                </div>
                <div class="col">
                    <p class="text-end"><?= $currencySymbols; ?> <?php echo $wallet_balance; ?></p>
                </div>

            </div>
            <hr class="line" />
            <div class="fee row px-2 ">
                <div class="col">
                    <h6>Withdrawal Fee</h6>
                </div>
                <div class="col ">
                    <p class="text-end" style="color:red;"><?= W_CHARGES; ?>%</p>
                </div>
            </div>
            <div class="fee row px-2">
                <div class="col">
                    <h6>Service Fee</h6>
                </div>
                <div class="col ">
                    <p class="text-end"> <?= $currencySymbols; ?> <?= S_CHARGES; ?> / Transaction</p>
                </div>
            </div>

            <!-- <div class="row px-2">
                <div class="col">
                    <h6>Withdrawal Amount</h6>
                    <h6>Total</h6>
                </div>
                <div class="col ">
                    <p class="text-end">$ <span id="amt">0.0</span></p>
                </div>

            </div> -->
        </div>

        <div class="formdep m-3">
            <form action="" id="withdraw_form">
                <div class="deposits d-flex flex-column ">
                    <label for="amount">Amount</label>
                    <input type="text" class="px-3" name="amount" id="amount" value="0" required onkeypress="return isDecimal(event, this);" onchange="get_amount(this.value);" placeholder="0">
                </div>
                <div class="deposits d-flex flex-column ">
                    <label for="wallet_address">Wallet Address</label>
                    <select name="wallet_address" id="wallet_address" required>
                        <?php
                        $sel_network = dbQuery("SELECT * FROM tabl_bank_details WHERE `status`='1' AND user_id='" . $user['id'] . "'");
                        while ($res_network = dbFetchAssoc($sel_network)) {
                        ?>
                            <option value="<?php echo $res_network['id'] ?>"><?php echo $res_network['network_name'] . ' - ' . $res_network['wallet_address']; ?></option>
                        <?php
                        }
                        ?>
                    </select>
                </div>

                <div class="deposits d-flex flex-column mt-2">
                    <label for="fund_password">Withdraw Password</label>
                    <input type="password" class="px-3" name="fund_password" id="fund_password" required placeholder="Enter Withdraw Password">

                    <div class="next mt-4">
                        <!-- <a class="button" href="withdrawprocessing.php"> WITHDRAW</a> -->

                        <div class="msg"></div>

                        <button type="submit" id="submit" name="submit" class="button">WITHDRAW</button>
                    </div>
            </form>
        </div>

    </div>




    <!-- <script src="assets/nav.js"></script> -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>


    <script>
        function isDecimal(evt, obj) {

            var charCode = (evt.which) ? evt.which : event.keyCode
            var value = obj.value;
            var dotcontains = value.indexOf(".") != -1;
            if (dotcontains)
                if (charCode == 46) return false;
            if (charCode == 46) return true;
            if (charCode > 31 && (charCode < 48 || charCode > 57))
                return false;
            return true;
        }


        function get_amount(val) {
            var amt = document.getElementById("amt");
            var max = <?php echo $wallet_balance; ?>;

            if (val < 10) {
                amt.innerHTML = 0;
                alert("invalid amount!");
            } else if (val <= max) {
                amt.innerHTML = round_num(val - (val * (<?= W_CHARGES ?> / 100)) - <?= S_CHARGES ?>);
            } else {
                amt.innerHTML = 0;
                alert("insufficient balance!");
            }
        }

        function round_num(num) {
            return Number(num.toFixed(2));
        }
    </script>


    <script>
        $("#withdraw_form").submit(function(e) {
            $("#submit").attr('disabled', true);
            $("#submit").html('<i class="spinner-border align-self-center" style="width="20" height="20"></i> processing...');
            $.ajax({
                url: 'ajax/withdraw.php',
                type: 'post',
                data: $("#withdraw_form").serialize(),
                success: function(data) {
                    if (data == 1) {
                        $("#submit").html('REGISTER');
                        $(".msg").html('<span style="color:green">Withdraw request addedd successfully!</span>');

                        setTimeout(function() {
                            window.location.href = 'withdrawprocessing.php';
                            // location.reload();
                        }, 1000);
                    } else {
                        $("#submit").attr('disabled', false);
                        $("#submit").html('WITHDRAW');
                        $(".msg").html(data);
                        setTimeout(function() {
                            $(".msg").html('');
                        }, 8000);
                    }
                },
            });
            e.preventDefault();
        });
    </script>
</body>

</html>