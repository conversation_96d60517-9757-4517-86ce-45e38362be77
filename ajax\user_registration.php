<?php
session_start();
include('../admin/lib/db_connection.php');
include('../admin/lib/get_functions.php');
include('../admin/inc/resize-class.php');
$page = 2;
$sub_page = 22;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

if (isset($_REQUEST['action']) && $_REQUEST['action'] == 'submit') {

    // $fname           =   mysqli_real_escape_string($con, $_REQUEST['fname']);
    // $lname           =   mysqli_real_escape_string($con, $_REQUEST['lname']);

    // $name           =   mysqli_real_escape_string($con, $_REQUEST['name']);

    $email          =   mysqli_real_escape_string($con, $_REQUEST['email']);
    $phone          =   mysqli_real_escape_string($con, $_REQUEST['phone']);
    $country_code          =   mysqli_real_escape_string($con, $_REQUEST['country_code']);
    $ref_code       =   mysqli_real_escape_string($con, $_REQUEST['ref_code']);
    $password       =   mysqli_real_escape_string($con, $_POST['password']);
    $con_password   =   mysqli_real_escape_string($con, $_POST['con_password']);
    $fund_password  =   mysqli_real_escape_string($con, $_POST['fund_password']);

    // $own_code       =   mysqli_real_escape_string($con, $_REQUEST['username']);
    $name       =   mysqli_real_escape_string($con, $_REQUEST['username']);

    // $name = $own_code;

    //default.png
    $sel_user = dbQuery("SELECT * FROM tabl_user WHERE email='" . $email . "' OR phone='" . $phone . "'");

    if (!$res_user = dbFetchAssoc($sel_user)) {

        // $sel_username = dbQuery("SELECT * FROM tabl_user WHERE sponsor_id='" . $own_code . "'");
        // if (!$res_username = dbFetchAssoc($sel_username)) {

        if ($password == $con_password) {

            $sel_sponsor = dbQuery("SELECT * FROM tabl_user WHERE sponsor_id='" . $ref_code . "'");

            if ($res_sponsor = dbFetchAssoc($sel_sponsor)) {

                $pass = password_hash($password, PASSWORD_BCRYPT);
                $fund_pass = password_hash($fund_password, PASSWORD_BCRYPT);

                $coins = 0;
                $amount = 0;
                // $own_code       =   getToken(10);
                $sponsor_code   =   $ref_code;
                $status         =   1;
                $user_type      =   1;

                $upliner = dbQuery("SELECT id as upliner_id FROM tabl_user WHERE sponsor_id='" . $ref_code . "'");
                $res_upliner = dbFetchAssoc($upliner);


                // $result = dbQuery("INSERT INTO tabl_user SET `name`='" . $name . "',sponsor_id='" . $own_code . "',upliner_id='" . $res_sponsor['id'] . "',phone='" . $phone . "',email='" . $email . "',password='" . $pass . "',fund_password='" . $fund_pass . "',user_image='default.png',status=1,date_added='" . $date . "'");

                $result = dbQuery("INSERT INTO tabl_user SET `name`='" . $name . "',upliner_id='" . $res_sponsor['id'] . "',country_code='" . $country_code . "',phone='" . $phone . "',email='" . $email . "',password='" . $pass . "',fund_password='" . $fund_pass . "',user_image='default.png',status=1,date_added='" . $date . "'");
                $user_id = dbInsertId();

                // echo "INSERT INTO tabl_user SET `name`='" . $own_code . "',sponsor_id='" . $own_code . "',upliner_id='" . $res_sponsor['id'] . "',phone='" . $phone . "',email='" . $email . "',password='" . $pass . "',fund_password='" . $fund_password . "',user_image='default.png',status=1,date_added='" . $date . "'";

                // $result = dbQuery("INSERT INTO tabl_user SET fname='" . $fname . "',lname='" . $lname . "',email='" . $email . "',phone='" . $phone . "',own_code='" . $own_code . "',sponsor_code='" . $sponsor_code . "',password='" . $pass . "',status='" . $status . "', date='" . $date . "'");

                if ($result) {

                    $own_code = 'DM' . sprintf('%08d', $user_id);

                    // $result2 = dbQuery("INSERT INTO tabl_wallet SET user_id='" . $user_id . "',user_type='" . $user_type . "',amount='" . $amount . "',status='" . $status . "', date='" . $date . "'");

                    $result3 = dbQuery("UPDATE tabl_user SET `sponsor_id`='" . $own_code . "' WHERE id='" . $user_id . "'");

                    // echo "INSERT INTO tabl_user SET fname='" . $fname . "',lname='" . $lname . "',email='" . $email . "',phone='" . $phone . "',ref_code='" . $ref_code . "',password='" . $password . "',status='" . $status . "', date='" . $date . "'";

                    echo 1 . '~' . $user_id;
                } else {
                    echo 0;
                }
            } else {
                echo 5;
            }
        } else {
            echo 3;
        }
        // } else {
        //     echo 6;
        // }
    } else {
        echo 2;
    }
} else {
    echo 4;
}
