<?php
session_start();
error_reporting(0);
include('admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

function calculateDirectJoiningCount($user_id)
{

    $min_price = 100 * USD_TO_INR;

    // Count direct users with a minimum investment of $100
    $query = dbQuery("
        SELECT COUNT(*) as total_users 
        FROM tabl_user 
        WHERE upliner_id = '$user_id' 
        AND id IN (SELECT user_id FROM tabl_plan_transaction WHERE price >= '$min_price' AND status = 1)
    ");

    $result = dbFetchAssoc($query);
    echo $result['total_users'] ?? 0;
    return $result['total_users'] ?? 0;
}


// Example usage:
// $topUserId = 1; // Replace with the actual top user id
// $result = calculateLeadershipInvestment($topUserId);
// echo "First Hand Total: " . $result['first_hand'] . " USD<br>";
// echo "Second Hand Total: " . $result['second_hand'] . " USD<br>";
// echo "Grand Total: " . $result['grand_total'] . " USD<br>";




// Leadership Income Processing Loop
$sel = dbQuery("SELECT id FROM tabl_user WHERE membership_id != 0");

while ($res = dbFetchAssoc($sel)) {
    $user_id = $res['id'];
    $today = date('Y-m-d H:i:s');

    echo "Processing Direct Joining Income for User: $user_id<br>";

    // Get count of direct users with min $100 investment
    $directJoiningCount = calculateDirectJoiningCount($user_id);

    if ($directJoiningCount > 0) {
        // Get the highest Direct Joining Income plan user qualifies for
        $joiningPlan = dbQuery("
            SELECT id, total_joining, investment, duration, bonus 
            FROM tabl_direct_joining_income 
            WHERE total_joining <= '$directJoiningCount' 
            ORDER BY total_joining DESC 
            LIMIT 1
        ");

        if ($plan = dbFetchAssoc($joiningPlan)) {
            $planId = $plan['id'];
            $durationDays = $plan['duration'];
            $bonusAmount = $plan['bonus'] * USD_TO_INR;

            // Check if user already received this bonus within duration
            $lastBonus = dbQuery("SELECT date_added 
                FROM tabl_direct_joining_income_records 
                WHERE user_id='$user_id' 
                AND direct_income_id='$planId' 
                ORDER BY date_added  
                LIMIT 1
            ");

            $record = dbFetchAssoc($lastBonus);
            if ($record) {
                $firstIncomeDate = $record['date_added'];
                $daysSinceFirstIncome = (strtotime($today) - strtotime($firstIncomeDate)) / (60 * 60 * 24);

                if ($daysSinceFirstIncome < $durationDays) {
                    echo "User $user_id is still within duration ($daysSinceFirstIncome days). Skipping bonus.<br>";
                    continue;
                }
            }


            // Check earning limit for upliner
            $refer_wallet = dbQuery("SELECT SUM(receive_amount) as total_earning FROM tabl_main_wallet WHERE user_id='" . $user_id . "'");
            $res_wallet = dbFetchAssoc($refer_wallet);

            if (($res_wallet['total_earning'] + $rewardAmount)  < $res_user['earning_limit']) {

                // Give Direct Joining Income
                dbQuery("INSERT INTO tabl_main_wallet SET 
                user_id='$user_id', 
                receive_amount='$bonusAmount', 
                type=1, 
                type_id=10, 
                narration='Direct Joining Income', 
                date_added='$date'
            ");

                // Store record
                dbQuery("INSERT INTO tabl_direct_joining_income_records SET 
                direct_income_id='$planId', 
                user_id='$user_id', 
                total_users='$directJoiningCount', 
                reward_amount='$bonusAmount', 
                duration='$durationDays', 
                date_added='$date'
            ");

                echo "Direct Joining Income of $bonusAmount INR added for User $user_id.<br>";
            } else {
                echo "Income limit exceeded for User $user_id.<br>";
            }
        }
    }
}
