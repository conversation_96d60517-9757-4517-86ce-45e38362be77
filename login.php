<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Login Page</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        body {
            background-color: #e5dbed;

        }


        .login-container {
            max-width: 300px;
            margin: 0 auto;
            padding: 20px;
            margin-top: 50px;
            margin-bottom: 50px;
            box-shadow: 0 0 10px rgba(156, 24, 176, 0.1);
            background-color: #ffffff;
            border-radius: 8px;
        }

        .login-container h2 {

            color: #823fb5;
            margin-bottom: 30px;
            text-align: center;
            font-size: 22px;
            font-weight: bold;
        }

        .login-container form {
            margin-bottom: 15px;
        }

        .login-container .btn-primary {
            background-color: #823fb5;
            border-color: #823fb5;
            width: 100%;
        }

        .login-container .btn-primary:hover {
            background: none;
            border: 1px solid #823fb5;
            color: #823fb5;

        }


        .forgot-password,
        .register-link {
            text-align: center;
            margin-top: 15px;
        }

        .forgot-password a,
        .register-link a {
            color: #823fb5;
        }

        .forgot-password a:hover,
        .register-link a:hover {
            text-decoration: underline;
        }

        .form-control {
            border-radius: 20px;
        }

        .login-container .btn-primary {
            background-color: #823fb5;
            border-color: #823fb5;
            width: 100%;
            border-radius: 20px;
        }

        .image {
            text-align: center;
            background-color: #823fb5;
        }

        .image img {
            width: 30%;
        }

        .required::before {
            content: "*";
            color: red;
            margin: 5px;
        }


        .eye_icon {
            border-radius: 0px 20px 20px 0px !important;
        }

        .pointer {
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- <div class="circle">
            <img src="assets/image/logo.jpeg" alt="Example Image">
        </div> -->

        <div class="login-container">
            <!-- <div class="image">
           <img src="assets/image/logo-removebg-preview (1).png" alt="">
        </div> -->
            <h2>Login</h2>
            <form post="post" id="loginform">
                <div class="form-group">

                    <label class="required" for="mobileNumber">Phone or Email</label>

                    <input type="text" class="form-control" id="name" name="username" placeholder="Enter phone no or email id">
                </div>
                <div class="form-group">
                    <label class="required" for="password">Password</label>
                    <!-- <input type="password" class="form-control" id="password" name="password" placeholder="Please enter password"> -->

                    <div class="input-group mb-3">
                        <input type="password" class="form-control" id="password" name="password" placeholder="Please enter password">
                        <div class="input-group-prepend">
                            <span class="input-group-text eye_icon pointer" onclick="show_pass('password');">
                                <i class="fa fa-eye" aria-hidden="true"></i>
                            </span>
                        </div>
                    </div>

                </div>

                <input type="hidden" name="action" value="submit" required>
                <button type="submit" class="btn btn-primary">Login</button>

                <div class="loader_login mt-2"></div>

            </form>
            <div class="forgot-password">
                <a href="resetpassword.php">Forgot Password?</a>
            </div>
            <div class="register-link">
                <p>Don't have an account yet? <a href="register.php">Register</a></p>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.0.7/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>


    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>


    <script>
        var admin_email = '<EMAIL>';

        $("#loginform").submit(function(e) {

            $(".submit").attr('disabled', true);
            $(".loader_login").html('<i class="spinner-border text-primary  align-self-center"></i> please wait...');
            $.ajax({
                url: 'ajax/login.php',
                type: 'post',
                data: $("#loginform").serialize(),
                success: function(data) {
                    //alert(data);
                    if (data == 1) {
                        $(".loader").hide();
                        $(location).attr('href', './index.php');

                    } else if (data == 2) {
                        $(".submit").attr('disabled', false);
                        $(".loader_login").html('<div class="alert alert-danger" role="alert">Either username or Password are wrong!</div>');
                        setTimeout(function() {
                            $(".loader_login").html('');

                        }, 8000);
                    } else if (data == 3) {
                        $(".submit").attr('disabled', false);
                        $(".loader_login").html('<div class="alert alert-danger" role="alert">Your Account is not Verified, please try later or contact to ' + admin_email + '</div>');
                        setTimeout(function() {
                            $(".loader_login").html('');

                        }, 8000);
                    } else if (data == 4) {
                        $(".submit").attr('disabled', false);
                        $(".loader_login").html('<div class="alert alert-danger" role="alert">Your Account is Inactive!</div>');
                        setTimeout(function() {
                            $(".loader_login").html('');

                        }, 8000);
                    } else {
                        $(".submit").attr('disabled', false);
                        $(".loader_login").html('<div class="alert alert-danger" role="alert">Account Does Not Exist!</div>');
                        setTimeout(function() {
                            $(".loader_login").html('');

                        }, 8000);
                    }
                },
            });

            e.preventDefault(); // avoid to execute the actual submit of the form.

        });

        function isNumberKey(evt) {
            var charCode = (evt.which) ? evt.which : evt.keyCode
            if (charCode > 31 && (charCode < 48 || charCode > 57))
                return false;
            return true;
        }


        function show_pass(inputId) {
            var inputElement = document.getElementById(inputId);
            var eyeIcon = inputElement.parentElement.querySelector('.eye_icon i');

            if (inputElement.type === "password") {
                inputElement.type = "text";
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                inputElement.type = "password";
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }
    </script>

</body>

</html>