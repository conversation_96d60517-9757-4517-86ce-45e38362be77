<?php 
session_start();
include('../lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d H:i:s');

$sel=dbQuery("SELECT * FROM tabl_withdrawl WHERE id='".$_REQUEST['w_id']."'");
	$res=dbFetchAssoc($sel);


if($_REQUEST['val']==1){
	
	  dbQuery("UPDATE tabl_main_wallet SET w_status=1 WHERE w_id='".$_REQUEST['w_id']."'");
	  
	  dbQuery("INSERT INTO tabl_admin_wallet SET user_id='".$res['user_id']."',paid_amount='".$res['amount']."',type=2,narration='Withdraw Request Completed',date_added='".$date."'");
	}
elseif($_REQUEST['val']==2){

	dbQuery("DELETE FROM tabl_main_wallet WHERE w_id='".$_REQUEST['w_id']."'");
	
}
	
dbQuery("UPDATE tabl_withdrawl SET status='".$_REQUEST['val']."' WHERE id='".$_REQUEST['w_id']."'");
echo '1';
 
?>