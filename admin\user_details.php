<?php
session_start();
error_reporting(0);
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 3;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

if (isset($_REQUEST['submit'])) {
  $id = $_REQUEST['id'];
  $sponsor_id = $_REQUEST['sponsor_id'];
  $phone = $_REQUEST['phone'];
  $email = $_REQUEST['email'];
  $name = mysqli_real_escape_string($con, $_REQUEST['name']);
  $trc_id = mysqli_real_escape_string($con, $_REQUEST['trc_id']);

  // Check if sponsor_id is already used by another user
  $sel = dbQuery("SELECT * FROM tabl_user WHERE sponsor_id='$sponsor_id' AND id!='$id'");
  $num_sponsor = dbNumRows($sel);

  if ($num_sponsor > 0) {
    echo '<script>alert("Sponsor ID not available!");window.location.href="user_details.php?id=' . $id . '";</script>';
  } else {
    // Check if phone number is already used by another user
    $sel_mobile = dbQuery("SELECT * FROM tabl_user WHERE phone='$phone' AND id!='$id'");
    $num_mobile = dbNumRows($sel_mobile);

    if ($num_mobile > 0) {
      echo '<script>alert("Phone number not available!");window.location.href="user_details.php?id=' . $id . '";</script>';
    } else {
      // Update user information
      $update_sql = "
                UPDATE tabl_user 
                SET name='$name', sponsor_id='$sponsor_id', phone='$phone', email='$email', trc_id='$trc_id' 
                WHERE id='$id'
            ";
      dbQuery($update_sql);

      echo '<script>alert("Profile updated successfully!");window.location.href="user_details.php?id=' . $id . '";</script>';
    }
  }
}

$sel = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_REQUEST['id'] . "'");
$res = dbFetchAssoc($sel);

?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
  <title><?php echo SITE; ?>- User Details</title>
  <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
  <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
  <script src="assets/js/loader.js"></script>

  <!-- BEGIN GLOBAL MANDATORY STYLES -->
  <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
  <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
  <!-- END GLOBAL MANDATORY STYLES -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
  <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
  <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
  <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

  <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
  <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
</head>

<body class="alt-menu sidebar-noneoverflow">
  <?php include('inc/__header.php');
  //print_r($_SESSION);


  ?>
  <!--  BEGIN MAIN CONTAINER  -->
  <div class="main-container" id="container">
    <div class="overlay"></div>
    <div class="search-overlay"></div>

    <!--  BEGIN TOPBAR  -->
    <?php include('inc/__menu.php'); ?>
    <!--  END TOPBAR  -->

    <!--  BEGIN CONTENT PART  -->
    <div id="content" class="main-content">
      <div class="layout-px-spacing">
        <nav class="breadcrumb-one" aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">Home</a></li>
            <li class="breadcrumb-item" active>User Details</li>
          </ol>
        </nav>
        <div class="account-settings-container layout-top-spacing">
          <div class="account-content">
            <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
              <div class="row">
                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                  <form name="account" method="post" id="general-info" class="section general-info" enctype="multipart/form-data">
                    <div class="info">
                      <h6 class="">User Details</h6>
                      <div class="row">
                        <div class="col-lg-11 mx-auto">
                          <div class="row">
                            <div class="col-xl-9 col-lg-12 col-md-8 mt-md-0 ">
                              <div class="widget-content widget-content-area">

                                <div class="form-row mb-4">
                                  <div class="form-group col-md-6">
                                    <label for="inputEmail4"><strong>Full Name</strong></label>
                                    <input type="name" class="form-control" value="<?php echo $res['name']; ?>" name="name" id="name" placeholder="Enter name" required>
                                  </div>

                                  <div class="form-group col-md-6">
                                    <label for="inputPassword4"><strong>Sponsor ID</strong></label>
                                    <input type="text" class="form-control" value="<?php echo $res['sponsor_id']; ?>" name="sponsor_id" id="sponsor_id" placeholder="Enter Sponsor ID" required>
                                  </div>

                                  <div class="form-group col-md-6">
                                    <label for="inputEmail4"><strong>Email</strong></label>
                                    <input type="email" class="form-control" value="<?php echo $res['email']; ?>" name="email" id="email" placeholder="Enter Email" required>
                                  </div>

                                  <div class="form-group col-md-6">
                                    <label for="inputPassword4"><strong>Phone</strong></label>
                                    <input type="text" class="form-control" value="<?php echo $res['phone']; ?>" name="phone" id="phone" placeholder="Enter Mobile No." onkeypress="return isNumber(event,this)" required>
                                  </div>


                                  <div class="form-group col-md-12">
                                    <label for="inputPassword4"><strong> BEP20 Link/ID</strong></label>
                                    <input type="text" class="form-control" value="<?php echo $res['trc_id']; ?>" name="trc_id" id="trc_id" placeholder="Enter BEP20 Link/ID">
                                  </div>

                                  <div class="form-group col-md-6">
                                    <button type="submit" name="submit" class="btn btn-primary mb-2">Update</button>
                                  </div>
                                </div>

                              </div>
                            </div>

                            <div class="col-xl-3 col-lg-12 col-md-8 mt-md-0 ">
                              <div class="widget-content widget-content-area">
                                <h5>Referral Details:</h5>
                                <div class="form-row mb-4">
                                  <?php $upliner_name = dbQuery("SELECT * FROM tabl_user WHERE id='" . $res['upliner_id'] . "'");
                                  $res_upliner_name = dbFetchAssoc($upliner_name); ?>

                                  <div class="form-group col-md-12">
                                    <label for="inputEmail4"><strong>Referral ID</strong></label>
                                    <input type="name" class="form-control" value="<?php echo $res_upliner_name['sponsor_id']; ?>" name="upliner_id" id="upliner_id" placeholder="Upliner ID" disabled required>
                                  </div>

                                  <div class="form-group col-md-12">
                                    <label for="inputEmail4"><strong>Referral Name</strong></label>
                                    <input type="name" class="form-control" value="<?php echo $res_upliner_name['name']; ?>" name="upliner_name" id="upliner_name" disabled placeholder="Upliner Name" required>
                                  </div>

                                </div>

                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
        <?php include('inc/__footer.php'); ?>
      </div>
    </div>
    <!--  END CONTENT PART  -->

  </div>
  <!-- END MAIN CONTAINER -->

  <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
  <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
  <script src="bootstrap/js/popper.min.js"></script>
  <script src="bootstrap/js/bootstrap.min.js"></script>
  <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
  <script src="assets/js/app.js"></script>
  <script>
    $(document).ready(function() {
      App.init();
    });
  </script>
  <script src="assets/js/custom.js"></script>
  <!-- END GLOBAL MANDATORY SCRIPTS -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
  <script src="plugins/apex/apexcharts.min.js"></script>
  <script src="assets/js/dashboard/dash_2.js"></script>
  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

  <script src="plugins/dropify/dropify.min.js"></script>
  <script src="plugins/blockui/jquery.blockUI.min.js"></script>
  <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
  <script src="assets/js/users/account-settings.js"></script>

  <script>
    function isNumber(evt) {
      var iKeyCode = (evt.which) ? evt.which : evt.keyCode
      if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
        return false;

      return true;
    }

    function isDecimal(evt, obj) {

      var charCode = (evt.which) ? evt.which : event.keyCode
      var value = obj.value;
      var dotcontains = value.indexOf(".") != -1;
      if (dotcontains)
        if (charCode == 46) return false;
      if (charCode == 46) return true;
      if (charCode > 31 && (charCode < 48 || charCode > 57))
        return false;
      return true;
    }
  </script>

  <script>
    var loadFile = function(event) {
      var image = document.getElementById('output');
      $(".loader").hide();
      $("#submit").attr('disabled', false);
      image.src = URL.createObjectURL(event.target.files[0]);
    };
  </script>
  <script>
    var loadFile1 = function(event) {
      var image = document.getElementById('output1');
      $(".loader").hide();
      $("#submit").attr('disabled', false);
      image.src = URL.createObjectURL(event.target.files[0]);
    };
  </script>



</body>

</html>