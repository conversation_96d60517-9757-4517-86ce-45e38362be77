<?php
session_start();
include('../admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

$sel = dbQuery("SELECT * FROM tabl_user WHERE sponsor_id='" . $_REQUEST['username'] . "'");
$num = dbNumRows($sel);

if ($num > 0) {
	echo 2;
	die();
} else {

	$sel_mobile = dbQuery("SELECT * FROM tabl_user WHERE phone='" . $_REQUEST['phone'] . "'");
	$num_mobile = dbNumRows($sel_mobile);
	if ($num_mobile > 0) {
		echo 3;
		die();
	} else {

		$refferal = dbQuery("SELECT * FROM tabl_user WHERE sponsor_id='" . $_REQUEST['ref_code'] . "'");
		$num_refferal = dbNumRows($refferal);
		if ($num_refferal == 0) {
			echo 4;
			die();
		} else {

			$otp = $_REQUEST['digit-1'] . '' . $_REQUEST['digit-2'] . '' . $_REQUEST['digit-3'] . '' . $_REQUEST['digit-4'];

			$sel_otp = dbQuery("SELECT * FROM tabl_login_otp WHERE email='" . $_REQUEST['email'] . "' AND otp='" . $otp . "'");
			$num_otp = dbNumRows($sel_otp);
			if ($num_otp == 0) {
				echo 5;
				die();
			} else {

				$upliner = dbQuery("SELECT id as upliner_id FROM tabl_user WHERE sponsor_id='" . $_REQUEST['refferal_code'] . "'");
				$res_upliner = dbFetchAssoc($upliner);


				dbQuery("INSERT INTO tabl_user SET name='" . mysqli_real_escape_string($con, $_REQUEST['username']) . "',sponsor_id='" . mysqli_real_escape_string($con, $_REQUEST['username']) . "',upliner_id='" . $res_upliner['upliner_id'] . "',phone='" . mysqli_real_escape_string($con, $_REQUEST['phone']) . "',email='" . mysqli_real_escape_string($con, $_REQUEST['email']) . "',pincode='" . mysqli_real_escape_string($con, $_REQUEST['pincode']) . "',password='" . md5($_REQUEST['password']) . "',user_image='default.png',status=1,date_added='" . $date . "'");
				
				$to = $_REQUEST['email'];
				// Subject
				$subject = 'Welcome to BTC Trade ';
				// Message
				$message = '<html>
				<body>
				<h1>BTC Trade </h1> 
				<div class="text" style="padding: 0 3em;">
				<h2>Welcome to BTC Trade !!</h2>
				<p>Hello ' . ucfirst($_REQUEST['username']) . ',<br/>
				Welcome to GET TOUCH we are greeting to you join us and very thankful to you trust us.</p> 
				<p>Regards<br/>
				Team BTC Trade </p>
				</div>
				</html>';
				// To send HTML mail, the Content-type header must be set
				$headers[] = 'MIME-Version: 1.0';
				$headers[] = 'Content-type: text/html; charset=iso-8859-1';
				// Additional headers
				$headers[] = 'From: ' . SITE . ' <' . EMAIL . '>';
				// Mail it
				mail($to, $subject, $message, implode("\r\n", $headers));
				echo 1;
			}
		}
	}
}
