<?php
@session_start();
include('./admin/lib/db_connection.php');
include('./lib/login.php');
if (@$_SESSION['user_id'] == "") {

    if (isset($_COOKIE['username']) && isset($_COOKIE['password'])) {

        if ($_COOKIE['username'] != '' && $_COOKIE['password'] != '') {

            $username = $_COOKIE['username'];
            $password = $_COOKIE['password'];

            // echo "<script>alert('" . $username . "');</script>";

            $login = login_now($username, $password);
            if ($login == 1) {
            } else {
                setcookie('username', '', time() - 3600, '/');
                setcookie('password', '', time() - 3600, '/');

                setcookie('username', false);
                setcookie('password', false);

                echo "<script>window.location.href='./login.php';</script>";
            }
        } else {
            echo "<script>window.location.href='./login.php';</script>";
        }
    } else {
        echo "<script>window.location.href='./login.php';</script>";
    }
}

if (@$_SESSION['product_id'] != "") {
    $product_id = $_SESSION['product_id'];
    @$_SESSION['product_id'] = "";
    echo "<script>window.location.href='send_new_inquiry.php?product_id=" . $product_id . " ';</script>";
}


// $userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
// $user = dbFetchAssoc($userq);

$user_id = @$_SESSION['user_id'];

// $userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
$userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . @$_SESSION['user_id'] . "'");
if (!$user = dbFetchAssoc($userq)) {
    echo "<script>window.location.href='" . $base_url . "login.php';</script>";
}
