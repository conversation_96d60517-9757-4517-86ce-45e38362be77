<?php
session_start();
include('admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

// $payment_id=$_REQUEST['NP_id'];
$payment_id = 5996710374;

// $curl = curl_init();
// curl_setopt_array($curl, array(
//   CURLOPT_URL => 'https://api.nowpayments.io/v1/payment/'.$payment_id.'',
//   CURLOPT_RETURNTRANSFER => true,
//   CURLOPT_ENCODING => '',
//   CURLOPT_MAXREDIRS => 10,
//   CURLOPT_TIMEOUT => 0,
//   CURLOPT_FOLLOWLOCATION => true,
//   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
//   CURLOPT_CUSTOMREQUEST => 'GET',
//   CURLOPT_HTTPHEADER => array(
//     'x-api-key: MT47JQE-JEZ4Z4S-KSTQQZ5-KD9HDBA'
//   ),
// ));
// $response = curl_exec($curl);
// curl_close($curl);

//echo $response;

// $json = json_decode($response, true);

$plan = dbQuery("SELECT * FROM tabl_plans WHERE id='" . $_REQUEST['plan_id'] . "'");
$res_plan = dbFetchAssoc($plan);

$order_id = 'GET' . rand(10000000, 99999999);

$json['payment_status'] = 'finished';
$json['invoice_id'] = '1';
$json['order_id'] = $order_id;
$json['payment_id'] = $payment_id;
$json['purchase_id'] = $order_id;
$json['outcome_amount'] = $res_plan['price'];




if ($json['payment_status'] == 'finished') {
	$sel = dbQuery("SELECT * FROM tabl_payment_transaction WHERE invoice_id='" . $json['invoice_id'] . "' AND order_id='" . $_SESSION['order_id'] . "'");
	$res = dbFetchAssoc($sel);

	if ($res['status'] == 0) {

		$user_id = $res['user_id'];
		$plan_id = $res['plan_id'];
		$price = $res['amount'];

		$earning_limit = $price * 3;
		$self_bonus = $price * 10 / 100;

		//#####################Credit Self Bonus

		dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $user_id . "',receive_amount='" . $self_bonus . "',type=1,type_id=1,narration='Self Bonus',date_added='" . $date . "'");
		//#################### END Self Bonus

		//Refer Income

		//FIRST
		$refer1 = dbQuery("SELECT upliner_id,earning_limit FROM tabl_user WHERE id='" . $user_id . "'");
		$num_refer1 = dbNumRows($refer1);
		if ($num_refer1 > 0) {
			$res_refer1 = dbFetchAssoc($refer1);
			$refer1_wallet = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_refer1['upliner_id'] . "'");
			$res_refer1_wallet = dbFetchAssoc($refer1_wallet);
			if ($res_refer1_wallet['total_earning'] < $res_refer1['earning_limit']) {

				$refer_bonus1 = $price * 5 / 100;

				dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer1['upliner_id'] . "',receive_amount='" . $refer_bonus1 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Referral Bonus',date_added='" . $date . "'");
			}

			//END FIRST

			//SECOND			
			$refer2 = dbQuery("SELECT upliner_id,earning_limit FROM tabl_user WHERE id='" . $res_refer1['upliner_id'] . "'");
			$num_refer2 = dbNumRows($refer2);
			if ($num_refer2 > 0) {
				$res_refer2 = dbFetchAssoc($refer2);
				$refer2_wallet = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_refer1['upliner_id'] . "'");
				$res_refer2_wallet = dbFetchAssoc($refer2_wallet);
				if ($res_refer2_wallet['total_earning'] < $res_refer2['earning_limit']) {

					$refer_bonus2 = $price * 3 / 100;

					dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer2['upliner_id'] . "',receive_amount='" . $refer_bonus2 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Referral Bonus',date_added='" . $date . "'");
				}

				//END SECOND

				//THIRD


				$refer3 = dbQuery("SELECT upliner_id,earning_limit FROM tabl_user WHERE id='" . $res_refer2['upliner_id'] . "'");
				$num_refer3 = dbNumRows($refer3);
				if ($num_refer3 > 0) {
					$res_refer3 = dbFetchAssoc($refer3);
					$refer3_wallet = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_refer2['upliner_id'] . "'");
					$res_refer3_wallet = dbFetchAssoc($refer3_wallet);
					if ($res_refer3_wallet['total_earning'] < $res_refer3['earning_limit']) {

						$refer_bonus3 = $price * 2 / 100;

						dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer3['upliner_id'] . "',receive_amount='" . $refer_bonus3 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Referral Bonus',date_added='" . $date . "'");
					}

					//END THIRD

					//FOURTH



					$refer4 = dbQuery("SELECT upliner_id,earning_limit FROM tabl_user WHERE id='" . $res_refer3['upliner_id'] . "'");
					$num_refer4 = dbNumRows($refer4);
					if ($num_refer4 > 0) {
						$res_refer4 = dbFetchAssoc($refer4);
						$refer4_wallet = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_refer3['upliner_id'] . "'");
						$res_refer4_wallet = dbFetchAssoc($refer4_wallet);
						if ($res_refer4_wallet['total_earning'] < $res_refer4['earning_limit']) {

							$refer_bonus4 = $price * 1 / 100;

							dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer4['upliner_id'] . "',receive_amount='" . $refer_bonus4 . "',type=1,type_id=2,to_id='" . $user_id . "',narration='Referral Bonus',date_added='" . $date . "'");
						}
						//END FOURTH
					}
				}
			}
		}
		//End REFER INCOME Here

		//Start 

		$quantum = dbQuery("SELECT * FROM tabl_user WHERE upliner_id='" . $res_refer1['upliner_id'] . "'");
		$new_num_quantum = dbNumRows($quantum);
		$res_quantum = dbFetchAssoc($quantum);

		$refer_wallet_info = dbQuery("SELECT sum(receive_amount)as total_earning FROM tabl_main_wallet WHERE user_id='" . $res_refer1['upliner_id'] . "'");
		$res_refer_wallet_info = dbFetchAssoc($refer_wallet_info);
		if ($res_refer_wallet_info['total_earning'] < $res_quantum['earning_limit']) {

			if ($new_num_quantum >= 10 && $new_num_quantum < 30) {
				$reward = 50;
				dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer1['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 10 Direct',date_added='" . $date . "'");
			} elseif ($new_num_quantum >= 30 && $new_num_quantum < 50) {
				$reward = 100;
				dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer1['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 30 Direct',date_added='" . $date . "'");
			} elseif ($new_num_quantum >= 50 && $new_num_quantum < 70) {

				$reward = 150;
				dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer1['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 50 Direct',date_added='" . $date . "'");
			} elseif ($new_num_quantum >= 70 && $new_num_quantum < 100) {

				$reward = 200;
				dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer1['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 70 Direct',date_added='" . $date . "'");
			} elseif ($new_num_quantum >= 100) {

				$reward = 500;
				dbQuery("INSERT INTO tabl_main_wallet SET user_id='" . $res_refer1['upliner_id'] . "',receive_amount='" . $reward . "',type=1,type_id=4,narration='Quantum Reward For 100 Direct',date_added='" . $date . "'");
			}
		}
		//End Quantum Reward	  
		dbQuery("UPDATE tabl_user SET earning_limit='" . $earning_limit . "',membership_id='" . $plan_id . "' WHERE id='" . $user_id . "'");

		dbQuery("UPDATE tabl_payment_transaction SET payment_id='" . $json['payment_id'] . "',purchase_id='" . $json['purchase_id'] . "',status=1,outcome_amount='" . $json['outcome_amount'] . "' WHERE invoice_id='" . $json['invoice_id'] . "' AND order_id='" . $json['order_id'] . "'");

		$sel1 = dbQuery("SELECT * FROM tabl_user WHERE id='" . $user_id . "'");
		$res1 = dbFetchAssoc($sel1);

		$_SESSION['user_id'] = $res1['id'];
		$_SESSION['sponsor_id'] = $res1['sponsor_id'];
		// echo '<script>window.location.href="thanks.php";</script>';
		echo '<script>alert("Request Success!");window.location.href="index.php";</script>';
	}
} else {

	$sel1 = dbQuery("SELECT * FROM tabl_user WHERE id='" . $res['user_id'] . "'");
	$res1 = dbFetchAssoc($sel1);
	$_SESSION['user_id'] = $res1['id'];
	$_SESSION['sponsor_id'] = $res1['sponsor_id'];
	// echo '<script>window.location.href="dashboard.php";</script>';
	echo '<script>alert("Request Success!");window.location.href="index.php";</script>';
}
?>

<table>
	<tr>
		<td><img src="loader.gif" width="27" /></td>
		<td style="font-size: 20px;">Do not refresh the page or click the back button...</td>
	</tr>
</table>