<?php
session_start();
error_reporting(0);
include('admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

// Recursive function to calculate total investment for a user and its downlines up to 10 levels.
function getInvestmentForUser($userId, $level = 1, $maxLevel = 10)
{
    if ($level > $maxLevel) {
        return 0;
    }

    // Get the user's own investment from tabl_plan_transaction.
    $sql = "SELECT SUM(price) as total FROM tabl_plan_transaction WHERE user_id = '$userId'";
    $result = dbQuery($sql);
    $row = dbFetchAssoc($result);
    $investment = isset($row['total']) ? $row['total'] : 0;

    // Get the direct downlines of the user from tabl_user.
    $sql2 = "SELECT id FROM tabl_user WHERE upliner_id = '$userId'";
    $result2 = dbQuery($sql2);
    while ($child = dbFetchAssoc($result2)) {
        // Recursively add investments from each child (and their subsequent downlines).
        $investment += getInvestmentForUser($child['id'], $level + 1, $maxLevel);
    }

    return $investment;
}

// Main function to calculate leadership income based on the two hands.
function calculateLeadershipInvestment($topUserId)
{
    // Retrieve direct downlines (hands) of the top user.
    $sql = "SELECT id FROM tabl_user WHERE upliner_id = '$topUserId'";
    $result = dbQuery($sql);

    $hands = array(); // Array to store investment of each direct downline branch.
    while ($direct = dbFetchAssoc($result)) {
        // For each direct downline, calculate total investment including its entire branch (up to 10 levels).
        $investment = getInvestmentForUser($direct['id'], 1, 10);
        $hands[] = $investment;
    }

    // If there are no direct downlines, return zeros.
    if (empty($hands)) {
        return array('first_hand' => 0, 'second_hand' => 0, 'grand_total' => 0);
    }

    // Identify the hand with the maximum investment.
    $maxInvestment = max($hands);
    // Calculate the total investment across all hands.
    $grandTotal = array_sum($hands);
    // The second hand is the total investment from all other downline branches.
    $secondHand = $grandTotal - $maxInvestment;

    // Return the results.
    return array(
        'first_hand'  => $maxInvestment,
        'second_hand' => $secondHand,
        'grand_total' => $grandTotal
    );
}

// Example usage:
// $topUserId = 1; // Replace with the actual top user id
// $result = calculateLeadershipInvestment($topUserId);
// echo "First Hand Total: " . $result['first_hand'] . " USD<br>";
// echo "Second Hand Total: " . $result['second_hand'] . " USD<br>";
// echo "Grand Total: " . $result['grand_total'] . " USD<br>";




// Leadership Income Processing Loop
$sel = dbQuery("SELECT id FROM tabl_user WHERE membership_id != 0");

while ($res = dbFetchAssoc($sel)) {
    $user_id = $res['id'];
    // $today = date('Y-m-d');
    $today = $date;

    // Check if Leadership Income was already given today
    $checkLeadership = dbQuery("SELECT id FROM tabl_leadership_income_records WHERE user_id='$user_id' AND date_added='$today' LIMIT 1");
    if (dbFetchAssoc($checkLeadership)) {
        echo "User $user_id has already received Leadership Income today.<br>";
        continue;
    }

    // Get the first given leadership income record
    $firstLeadershipRecord = dbQuery("SELECT date_added, duration FROM tabl_leadership_income_records WHERE user_id='$user_id' ORDER BY date_added ASC LIMIT 1");
    if ($firstRecord = dbFetchAssoc($firstLeadershipRecord)) {
        $firstGivenDate = $firstRecord['date_added'];
        $durationDays = $firstRecord['duration'];
        $daysSinceFirstGiven = (strtotime($today) - strtotime($firstGivenDate)) / 86400; // Convert seconds to days

        // echo $daysSinceFirstGiven;

        // Stop giving leadership income if duration has passed
        if ($daysSinceFirstGiven >= $durationDays) {
            echo "User $user_id has reached leadership income duration limit.<br>";
            continue;
        }
    }

    // Calculate Leadership Investment
    $leadershipInvestment = calculateLeadershipInvestment($user_id);
    $firstHand = $leadershipInvestment['first_hand'];
    $secondHand = $leadershipInvestment['second_hand'];
    $totalInvestment = $leadershipInvestment['grand_total'];

    // Ensure 1st hand is greater than 60% of total investment
    if ($totalInvestment > 0 && $firstHand >= ($totalInvestment * 0.6)) {
        echo "User $user_id qualifies for Leadership Income!<br>";
        $totalInvestmentUSD = $totalInvestment / USD_TO_INR;
        // echo "Total Investment USD : " . $totalInvestmentUSD . "<br>";

        // Get only ONE plan with the highest eligible investment
        $leadershipPlan = dbQuery("SELECT id, investment, reward, duration FROM tabl_leadership_income WHERE investment <= '$totalInvestmentUSD' ORDER BY investment DESC LIMIT 1");

        if ($plan = dbFetchAssoc($leadershipPlan)) {
            $leadershipIncomeId = $plan['id'];
            $rewardAmount = $plan['reward'] * USD_TO_INR;
            $durationDays = $plan['duration'];

            // Check earning limit for upliner
            $refer_wallet = dbQuery("SELECT SUM(receive_amount) as total_earning FROM tabl_main_wallet WHERE user_id='" . $user_id . "'");
            $res_wallet = dbFetchAssoc($refer_wallet);

            if (($res_wallet['total_earning'] + $rewardAmount)  < $res_user['earning_limit']) {

                // Insert Leadership Income
                dbQuery("INSERT INTO tabl_main_wallet SET 
                user_id='$user_id', 
                receive_amount='$rewardAmount', 
                type=1, 
                type_id=9, 
                narration='Leadership Income', 
                date_added='$today'");

                // Store record in leadership income tracking table
                dbQuery("INSERT INTO tabl_leadership_income_records SET 
                leadership_income_id='$leadershipIncomeId',
                user_id='$user_id',
                first_hand='$firstHand',
                second_hand='$secondHand',
                total_investment='$totalInvestment',
                reward_amount='$rewardAmount',
                duration='$durationDays',
                date_added='$today'");

                echo "Leadership income of $rewardAmount INR added for User $user_id.<br>";
            } else {
                echo "Income limit exceeded for User $user_id.<br>";
            }
        }
    }
}
