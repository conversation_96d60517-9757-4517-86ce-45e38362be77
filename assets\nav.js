let burger = document.querySelector('.burger');
let close = document.querySelector('.close');
let sidenav = document.querySelector('#sidenav');
let overlay = document.querySelector('#overlay');

let classOpen = [sidenav, overlay];
burger.addEventListener('click', function(e){
  classOpen.forEach(e => e.classList.add('active'));
});

let classCloseClick = [overlay, close];
classCloseClick.forEach(function(el) {
  el.addEventListener('click', function(els) {
    classOpen.forEach(els => els.classList.remove('active'));
  });
});